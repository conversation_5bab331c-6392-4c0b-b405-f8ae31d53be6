{"name": "mcp-function-calling", "version": "1.0.0", "description": "HTTP-based MCP Server and Client with OpenAI-style function calling", "main": "index.js", "type": "module", "scripts": {"start:server": "node server/index.js", "start:client": "node client/index.js", "start:weather-server": "node examples/weather-server.js", "start:math-server": "node examples/math-server.js", "start:all": "./start-all.sh", "stop:all": "./stop-all.sh", "dev:server": "node --watch server/index.js", "test": "node examples/test-client.js", "example": "node examples/simple-usage.js"}, "keywords": ["mcp", "function-calling", "openai", "server", "client", "http"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "axios": "^1.6.0", "uuid": "^10.0.0"}, "devDependencies": {"@types/node": "^20.0.0"}}