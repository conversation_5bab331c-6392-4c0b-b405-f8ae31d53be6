export const MCPMessageTypes = {
  LIST_TOOLS: 'tools/list',
  CALL_TOOL: 'tools/call',
  SERVER_INFO: 'server/info'
};

export const MCPResponseStatus = {
  SUCCESS: 'success',
  ERROR: 'error'
};

export class MCPError extends Error {
  constructor(message, code = 'UNKNOWN_ERROR', details = null) {
    super(message);
    this.name = 'MCPError';
    this.code = code;
    this.details = details;
  }

  toJSON() {
    return {
      error: {
        message: this.message,
        code: this.code,
        details: this.details
      }
    };
  }
}

export class MCPTool {
  constructor(name, description, parameters, handler) {
    this.name = name;
    this.description = description;
    this.parameters = parameters || { properties: {}, required: [] };
    this.handler = handler;
  }

  getSchema() {
    return {
      name: this.name,
      description: this.description,
      parameters: {
        type: 'object',
        properties: this.parameters.properties || {},
        required: this.parameters.required || []
      }
    };
  }

  async execute(args = {}) {
    try {
      const result = await this.handler(args);
      return {
        status: MCPResponseStatus.SUCCESS,
        data: result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw new MCPError(
        `Tool execution failed: ${error.message}`,
        'TOOL_EXECUTION_ERROR',
        { tool: this.name, args }
      );
    }
  }
}

export function validateToolParameters(tool, args) {
  const { required = [] } = tool.parameters;
  
  for (const param of required) {
    if (!(param in args)) {
      throw new MCPError(
        `Missing required parameter: ${param}`,
        'MISSING_PARAMETER',
        { tool: tool.name, missing: param, provided: Object.keys(args) }
      );
    }
  }
  
  return true;
}

export function createSuccessResponse(data, metadata = {}) {
  return {
    status: MCPResponseStatus.SUCCESS,
    data,
    metadata: {
      timestamp: new Date().toISOString(),
      ...metadata
    }
  };
}

export function createErrorResponse(error, metadata = {}) {
  const errorObj = error instanceof MCPError ? error : new MCPError(error.message || 'Unknown error');
  
  return {
    status: MCPResponseStatus.ERROR,
    error: {
      message: errorObj.message,
      code: errorObj.code,
      details: errorObj.details
    },
    metadata: {
      timestamp: new Date().toISOString(),
      ...metadata
    }
  };
}
