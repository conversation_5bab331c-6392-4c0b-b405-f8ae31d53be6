#!/bin/bash

echo "🚀 Starting all MCP servers..."

# Function to start a server in the background
start_server() {
    local name=$1
    local command=$2
    local port=$3
    
    echo "Starting $name server on port $port..."
    $command &
    local pid=$!
    echo "$pid" > ".${name}_server.pid"
    echo "✅ $name server started (PID: $pid)"
}

# Start all servers
start_server "default" "npm run start:server" "3000"
sleep 2
start_server "weather" "npm run start:weather-server" "3001"
sleep 2
start_server "math" "npm run start:math-server" "3002"

echo ""
echo "🎉 All servers started!"
echo ""
echo "Available endpoints:"
echo "  Default Server: http://localhost:3000"
echo "  Weather Server: http://localhost:3001"
echo "  Math Server:    http://localhost:3002"
echo ""
echo "To test the system:"
echo "  npm test"
echo ""
echo "To stop all servers:"
echo "  ./stop-all.sh"
echo ""
echo "Press Ctrl+C to stop all servers"

# Wait for interrupt
trap 'echo ""; echo "🛑 Stopping all servers..."; ./stop-all.sh; exit 0' INT
wait
