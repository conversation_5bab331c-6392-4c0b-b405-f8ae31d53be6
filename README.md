# HTTP-based MCP Function Calling Server & Client

A Model Context Protocol (MCP) implementation with OpenAI-style function calling capabilities using HTTP APIs in JavaScript.

## Features

- **HTTP-based Architecture**: Servers and clients communicate via REST APIs
- **Multi-Server Support**: Connect to multiple MCP servers simultaneously
- **Extensible MCP Servers**: Easy-to-create specialized servers with custom tools
- **Smart Tool Discovery**: Automatically find and call tools across multiple servers
- **OpenAI-style Function Calling**: Simulate OpenAI's function calling pattern
- **Built-in Tools**: Weather, math, UUID generation, and more
- **Custom Tools**: Easy registration of custom functions
- **Error Handling**: Comprehensive error handling and validation

## Quick Start

### Installation

```bash
npm install
```

### Starting Multiple Servers

```bash
# Terminal 1 - Default server (port 3000)
npm run start:server

# Terminal 2 - Weather server (port 3001)
npm run start:weather-server

# Terminal 3 - Math server (port 3002)
npm run start:math-server
```

### Running Tests

```bash
# Make sure all servers are running first
npm test
```

### Basic Usage

```javascript
import MCPClient from './client/index.js';

const client = new MCPClient();

// Connect to multiple servers
await client.addServer('default', 'http://localhost:3000');
await client.addServer('weather', 'http://localhost:3001');
await client.addServer('math', 'http://localhost:3002');

// Direct function call on specific server
const result = await client.callTool('weather', 'get_weather', {
  location: 'San Francisco, CA',
  unit: 'celsius'
});

// Smart tool discovery (finds tool on any server)
const mathResult = await client.findAndCallTool('calculate', {
  expression: '2 + 2 * 3'
});

// OpenAI-style function calling
const response = await client.simulateOpenAIFunctionCalling([
  { role: 'user', content: 'What is the weather in Tokyo?' }
]);
```

## Available Servers & Tools

### Default Server (Port 3000)
Basic utility tools:
- `get_server_info`: Get server information
- `generate_uuid`: Generate UUIDs
- `echo`: Echo back messages

### Weather Server (Port 3001)
Weather-related tools:
- `get_weather`: Get current weather for a location
- `get_forecast`: Get multi-day weather forecast
- `get_air_quality`: Get air quality information

### Math Server (Port 3002)
Mathematical tools:
- `calculate`: Basic mathematical calculations
- `advanced_math`: Advanced operations (factorial, fibonacci, prime check, gcd, lcm)
- `statistics`: Statistical analysis of datasets

## Tool Examples

### Weather Tools
```javascript
// Current weather
await client.callTool('weather', 'get_weather', {
  location: 'London, UK',
  unit: 'celsius'
});

// 5-day forecast
await client.callTool('weather', 'get_forecast', {
  location: 'New York, NY',
  days: 5,
  unit: 'fahrenheit'
});
```

### Math Tools
```javascript
// Basic calculation
await client.callTool('math', 'calculate', {
  expression: '2 + 2 * 3'
});

// Advanced math
await client.callTool('math', 'advanced_math', {
  operation: 'factorial',
  numbers: [5]
});

// Statistics
await client.callTool('math', 'statistics', {
  data: [1, 2, 3, 4, 5],
  measures: ['mean', 'median', 'std_dev']
});
```

## Creating Custom MCP Servers

### Basic Custom Server

```javascript
import MCPServer from './server/index.js';

const customServer = new MCPServer({
  name: 'My Custom MCP Server',
  description: 'A specialized server for custom operations',
  port: 3003
});

customServer.registerTool({
  name: 'my_custom_tool',
  description: 'Description of what the tool does',
  parameters: {
    properties: {
      param1: {
        type: 'string',
        description: 'Parameter description'
      },
      param2: {
        type: 'number',
        description: 'Another parameter'
      }
    },
    required: ['param1']
  },
  handler: async (args) => {
    const { param1, param2 } = args;

    // Your custom logic here
    return {
      success: true,
      result: `Processed ${param1} with ${param2}`,
      timestamp: new Date().toISOString()
    };
  }
});

await customServer.start();
```

### Connecting to Custom Servers

```javascript
const client = new MCPClient();
await client.addServer('custom', 'http://localhost:3003');

// Use the custom tool
await client.callTool('custom', 'my_custom_tool', {
  param1: 'hello',
  param2: 42
});
```

## Multi-Server Management

### Adding and Managing Servers

```javascript
const client = new MCPClient();

// Add multiple servers
await client.addServer('weather', 'http://localhost:3001');
await client.addServer('math', 'http://localhost:3002');

// List all connected servers
const servers = client.getServers();
console.log(servers);

// Remove a server
client.removeServer('weather');

// Get tools from specific server
const mathTools = await client.getServerTools('math');

// Get tools from all servers
const allTools = await client.getAllTools();
```

### Smart Tool Discovery

```javascript
// Automatically find and call tools across all servers
await client.findAndCallTool('get_weather', {
  location: 'Paris, France'
});

// With preferred server
await client.findAndCallTool('calculate', {
  expression: '2 + 2'
}, 'math');
```

## OpenAI-Style Function Calling

The client includes a simulation of OpenAI's function calling pattern:

```javascript
const messages = [
  { role: 'user', content: 'What is 15 * 7?' }
];

const response = await client.simulateOpenAIFunctionCalling(messages);

console.log(response);
// {
//   role: 'assistant',
//   content: 'I called the calculate function and got the following result: {...}',
//   function_call: {
//     name: 'calculate',
//     arguments: '{"expression":"15 * 7"}',
//     server: 'math'
//   },
//   function_result: { success: true, result: 105, ... }
// }
```

## API Reference

### MCPServer

#### Constructor
```javascript
new MCPServer({
  name: 'Server Name',
  description: 'Server description',
  port: 3000
})
```

#### Methods
- `registerTool(toolDefinition)`: Register a new tool
- `start()`: Start the HTTP server
- `stop()`: Stop the HTTP server

#### Tool Definition Schema
```javascript
{
  name: string,           // Tool name
  description: string,    // Tool description
  parameters: {           // OpenAI function schema
    properties: object,
    required: string[]
  },
  handler: async (args) => any  // Tool implementation
}
```

### MCPClient

#### Methods
- `addServer(name, baseUrl, options?)`: Connect to an MCP server
- `removeServer(name)`: Remove a server connection
- `getServers()`: Get list of connected servers
- `callTool(serverName, toolName, args)`: Call a tool on specific server
- `findAndCallTool(toolName, args, preferredServer?)`: Find and call tool on any server
- `getServerTools(serverName)`: Get tools from specific server
- `getAllTools()`: Get tools from all servers
- `simulateOpenAIFunctionCalling(messages)`: Simulate OpenAI function calling

## Examples

See the `examples/` directory for detailed examples:

- `examples/weather-server.js`: Weather-specialized MCP server
- `examples/math-server.js`: Math-specialized MCP server
- `examples/test-client.js`: Comprehensive multi-server client test

## Error Handling

All function calls include comprehensive error handling:

```javascript
try {
  const result = await client.callTool('math', 'calculate', {
    expression: 'invalid expression'
  });
} catch (error) {
  console.error('Tool call failed:', error.message);
  console.error('Error code:', error.code);
  console.error('Details:', error.details);
}
```

### HTTP API Endpoints

Each MCP server exposes these REST endpoints:

- `GET /health` - Health check
- `GET /server/info` - Server information
- `GET /tools/list` - List available tools
- `POST /tools/call` - Call a tool

## Development

### Project Structure

```
├── server/
│   └── index.js           # HTTP-based MCP server
├── client/
│   └── index.js           # Multi-server MCP client
├── shared/
│   └── types.js           # Shared types and utilities
├── examples/
│   ├── weather-server.js  # Weather MCP server
│   ├── math-server.js     # Math MCP server
│   └── test-client.js     # Multi-server test client
└── package.json
```

### Scripts

- `npm run start:server`: Start default MCP server (port 3000)
- `npm run start:weather-server`: Start weather server (port 3001)
- `npm run start:math-server`: Start math server (port 3002)
- `npm run dev:server`: Start default server with file watching
- `npm test`: Run comprehensive multi-server tests

## License

MIT
