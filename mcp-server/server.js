import express from 'express';
import cors from 'cors';
import axios from 'axios';
import fs from 'fs';
import yaml from 'js-yaml';
import path from 'path';

const app = express();
const PORT = 3000;

app.use(cors());
app.use(express.json());

// ElectronHub API configuration
const ELECTRONHUB_API_BASE = 'https://api.electronhub.ai/v1';
const ELECTRONHUB_API_KEY = 'ek-proxy-R4tBYVMxBMz7cH3dz3yfg5utVNaYuugooC4vQi2hB3hd';

// Global variables for dynamic tools
let dynamicTools = [];
let openApiSchema = null;

// Load and parse OpenAPI schema
function loadOpenApiSchema() {
  try {
    const schemaPath = path.join(process.cwd(), 'openapi.yml');
    if (fs.existsSync(schemaPath)) {
      const schemaContent = fs.readFileSync(schemaPath, 'utf8');
      openApiSchema = yaml.load(schemaContent);
      console.log('📋 Loaded OpenAPI schema:', openApiSchema.info?.title || 'Unknown API');
      generateDynamicTools();
    } else {
      console.log('⚠️  No openapi.yml found, using default AI tool');
      createDefaultAiTool();
    }
  } catch (error) {
    console.error('❌ Error loading OpenAPI schema:', error.message);
    createDefaultAiTool();
  }
}

// Create default AI tool when no OpenAPI schema is available
function createDefaultAiTool() {
  dynamicTools = [{
    name: 'ask_ai',
    description: 'Ask AI a question using ElectronHub',
    parameters: {
      type: 'object',
      properties: {
        question: {
          type: 'string',
          description: 'Question to ask the AI'
        }
      },
      required: ['question']
    },
    handler: async (args) => {
      const { question } = args;
      try {
        const response = await axios.post(`${ELECTRONHUB_API_BASE}/chat/completions`, {
          model: 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: question }],
          max_tokens: 150
        }, {
          headers: {
            'Authorization': `Bearer ${ELECTRONHUB_API_KEY}`,
            'Content-Type': 'application/json'
          }
        });

        return {
          question,
          answer: response.data.choices[0]?.message?.content || 'No response'
        };
      } catch (error) {
        throw new Error('AI request failed: ' + error.message);
      }
    }
  }];
}

// Generate dynamic tools from OpenAPI schema using AI
async function generateDynamicTools() {
  if (!openApiSchema) return;

  try {
    // Use AI to analyze the OpenAPI schema and create tool descriptions
    const schemaAnalysisPrompt = `Analyze this OpenAPI schema and create MCP tools for the most useful endpoints.

OpenAPI Schema:
${JSON.stringify(openApiSchema, null, 2)}

For each useful endpoint, create a tool with:
1. A clear, descriptive name (snake_case)
2. A helpful description of what it does
3. Parameters based on the endpoint's parameters/requestBody
4. Only include the most commonly used endpoints (max 10 tools)

Return a JSON array of tools in this format:
[
  {
    "name": "tool_name",
    "description": "What this tool does",
    "method": "GET|POST|PUT|DELETE",
    "path": "/api/path",
    "parameters": {
      "type": "object",
      "properties": {
        "param_name": {
          "type": "string",
          "description": "Parameter description"
        }
      },
      "required": ["param_name"]
    }
  }
]`;

    const response = await axios.post(`${ELECTRONHUB_API_BASE}/chat/completions`, {
      model: 'gpt-4',
      messages: [{ role: 'user', content: schemaAnalysisPrompt }],
      max_tokens: 2000
    }, {
      headers: {
        'Authorization': `Bearer ${ELECTRONHUB_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    const aiResponse = response.data.choices[0]?.message?.content;
    const jsonMatch = aiResponse.match(/\[[\s\S]*\]/);

    if (jsonMatch) {
      const toolDefinitions = JSON.parse(jsonMatch[0]);

      // Convert AI-generated tool definitions to executable tools
      dynamicTools = toolDefinitions.map(toolDef => ({
        name: toolDef.name,
        description: toolDef.description,
        parameters: toolDef.parameters,
        handler: createDynamicHandler(toolDef)
      }));

      // Add the AI tool for general questions
      dynamicTools.push({
        name: 'ask_ai',
        description: 'Ask AI a question when no specific tool matches',
        parameters: {
          type: 'object',
          properties: {
            question: { type: 'string', description: 'Question to ask' }
          },
          required: ['question']
        },
        handler: async (args) => {
          const response = await axios.post(`${ELECTRONHUB_API_BASE}/chat/completions`, {
            model: 'gpt-3.5-turbo',
            messages: [{ role: 'user', content: args.question }],
            max_tokens: 150
          }, {
            headers: {
              'Authorization': `Bearer ${ELECTRONHUB_API_KEY}`,
              'Content-Type': 'application/json'
            }
          });
          return { answer: response.data.choices[0]?.message?.content };
        }
      });

      console.log(`🛠️  Generated ${dynamicTools.length} dynamic tools from OpenAPI schema`);
    } else {
      throw new Error('Could not parse AI response');
    }
  } catch (error) {
    console.error('❌ Error generating dynamic tools:', error.message);
    createDefaultAiTool();
  }
}

// Create a dynamic handler for an OpenAPI endpoint
function createDynamicHandler(toolDef) {
  return async (args) => {
    try {
      const baseUrl = openApiSchema.servers?.[0]?.url || 'http://localhost';
      const url = `${baseUrl}${toolDef.path}`;

      // Build request configuration
      const config = {
        method: toolDef.method.toLowerCase(),
        url: url,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'MCP-Server/1.0'
        }
      };

      // Add authentication if specified in schema
      if (openApiSchema.components?.securitySchemes) {
        const securityScheme = Object.values(openApiSchema.components.securitySchemes)[0];
        if (securityScheme?.type === 'http' && securityScheme?.scheme === 'bearer') {
          // You would need to configure the bearer token
          // config.headers['Authorization'] = `Bearer ${process.env.API_TOKEN}`;
        } else if (securityScheme?.type === 'apiKey') {
          const keyName = securityScheme.name;
          if (securityScheme.in === 'header') {
            // config.headers[keyName] = process.env.API_KEY;
          } else if (securityScheme.in === 'query') {
            // Add to query params
          }
        }
      }

      // Handle path parameters
      let finalUrl = url;
      if (toolDef.path.includes('{')) {
        for (const [key, value] of Object.entries(args)) {
          finalUrl = finalUrl.replace(`{${key}}`, encodeURIComponent(value));
        }
        config.url = finalUrl;
      }

      // Handle query parameters and request body
      if (toolDef.method.toUpperCase() === 'GET') {
        // Add query parameters
        const queryParams = new URLSearchParams();
        for (const [key, value] of Object.entries(args)) {
          if (!toolDef.path.includes(`{${key}}`)) {
            queryParams.append(key, value);
          }
        }
        if (queryParams.toString()) {
          config.url += `?${queryParams.toString()}`;
        }
      } else {
        // Add request body for POST/PUT/PATCH
        config.data = args;
      }

      const response = await axios(config);

      return {
        success: true,
        status: response.status,
        data: response.data,
        endpoint: `${toolDef.method} ${toolDef.path}`,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        status: error.response?.status,
        endpoint: `${toolDef.method} ${toolDef.path}`,
        timestamp: new Date().toISOString()
      };
    }
  };
}

// Get current tools (dynamic or default)
function getCurrentTools() {
  return dynamicTools.map(tool => ({
    name: tool.name,
    description: tool.description,
    parameters: tool.parameters
  }));
}

// Routes
app.get('/tools', (req, res) => {
  res.json({ tools: getCurrentTools() });
});

app.post('/tools/call', async (req, res) => {
  const { name, arguments: args } = req.body;

  try {
    const tool = dynamicTools.find(t => t.name === name);
    if (!tool) {
      return res.status(400).json({ error: 'Unknown tool: ' + name });
    }

    const result = await tool.handler(args);
    res.json({ success: true, result });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    tools: dynamicTools.length,
    schema_loaded: !!openApiSchema,
    schema_title: openApiSchema?.info?.title || 'None'
  });
});

// Endpoint to reload OpenAPI schema
app.post('/reload-schema', async (req, res) => {
  try {
    loadOpenApiSchema();
    res.json({
      success: true,
      tools: dynamicTools.length,
      schema_title: openApiSchema?.info?.title || 'Default AI'
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Initialize server
async function startServer() {
  // Load OpenAPI schema and generate tools
  loadOpenApiSchema();

  app.listen(PORT, () => {
    console.log(`🚀 MCP Server running on port ${PORT}`);
    console.log(`📋 Available tools: ${dynamicTools.map(t => t.name).join(', ')}`);
    console.log(`🔗 Health: http://localhost:${PORT}/health`);
    console.log(`🛠️  Tools: http://localhost:${PORT}/tools`);
    console.log(`🔄 Reload schema: http://localhost:${PORT}/reload-schema`);
    if (openApiSchema) {
      console.log(`📖 OpenAPI Schema: ${openApiSchema.info?.title || 'Unknown'}`);
    } else {
      console.log(`📝 No OpenAPI schema found. Create 'openapi.yml' to add dynamic tools.`);
    }
  });
}

startServer().catch(console.error);
