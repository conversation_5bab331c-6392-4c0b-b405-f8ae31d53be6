import express from 'express';
import cors from 'cors';
import axios from 'axios';

const app = express();
const PORT = 3000;

app.use(cors());
app.use(express.json());

// ElectronHub API configuration
const ELECTRONHUB_API_BASE = 'https://api.electronhub.ai/v1';
const ELECTRONHUB_API_KEY = 'ek-proxy-R4tBYVMxBMz7cH3dz3yfg5utVNaYuugooC4vQi2hB3hd';

// Available tools
const tools = [
  {
    name: 'get_weather',
    description: 'Get weather information for a location',
    parameters: {
      type: 'object',
      properties: {
        location: {
          type: 'string',
          description: 'City name'
        }
      },
      required: ['location']
    }
  },
  {
    name: 'calculate',
    description: 'Perform mathematical calculations',
    parameters: {
      type: 'object',
      properties: {
        expression: {
          type: 'string',
          description: 'Math expression to calculate'
        }
      },
      required: ['expression']
    }
  },
  {
    name: 'ask_ai',
    description: 'Ask AI a question using ElectronHub',
    parameters: {
      type: 'object',
      properties: {
        question: {
          type: 'string',
          description: 'Question to ask the AI'
        }
      },
      required: ['question']
    }
  }
];

// Tool implementations
async function executeWeather(args) {
  const { location } = args;
  // Mock weather data
  return {
    location,
    temperature: Math.round(Math.random() * 30 + 10),
    condition: ['sunny', 'cloudy', 'rainy'][Math.floor(Math.random() * 3)],
    humidity: Math.round(Math.random() * 50 + 30)
  };
}

async function executeCalculate(args) {
  const { expression } = args;
  try {
    // Simple eval for basic math (sanitized)
    const sanitized = expression.replace(/[^0-9+\-*/().\s]/g, '');
    const result = eval(sanitized);
    return { expression, result };
  } catch (error) {
    throw new Error('Invalid math expression');
  }
}

async function executeAskAI(args) {
  const { question } = args;
  try {
    const response = await axios.post(`${ELECTRONHUB_API_BASE}/chat/completions`, {
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: question }],
      max_tokens: 150
    }, {
      headers: {
        'Authorization': `Bearer ${ELECTRONHUB_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    return {
      question,
      answer: response.data.choices[0]?.message?.content || 'No response'
    };
  } catch (error) {
    throw new Error('AI request failed: ' + error.message);
  }
}

// Routes
app.get('/tools', (req, res) => {
  res.json({ tools });
});

app.post('/tools/call', async (req, res) => {
  const { name, arguments: args } = req.body;
  
  try {
    let result;
    
    switch (name) {
      case 'get_weather':
        result = await executeWeather(args);
        break;
      case 'calculate':
        result = await executeCalculate(args);
        break;
      case 'ask_ai':
        result = await executeAskAI(args);
        break;
      default:
        return res.status(400).json({ error: 'Unknown tool: ' + name });
    }
    
    res.json({ success: true, result });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/health', (req, res) => {
  res.json({ status: 'ok', tools: tools.length });
});

app.listen(PORT, () => {
  console.log(`🚀 MCP Server running on port ${PORT}`);
  console.log(`📋 Available tools: ${tools.map(t => t.name).join(', ')}`);
  console.log(`🔗 Health: http://localhost:${PORT}/health`);
  console.log(`🛠️  Tools: http://localhost:${PORT}/tools`);
});
