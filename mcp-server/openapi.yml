openapi: 3.0.0
info:
  title: Example API
  description: A sample API for demonstration
  version: 1.0.0
servers:
  - url: https://jsonplaceholder.typicode.com
    description: JSONPlaceholder API

paths:
  /posts:
    get:
      summary: Get all posts
      description: Retrieve a list of all posts
      parameters:
        - name: userId
          in: query
          description: Filter posts by user ID
          schema:
            type: integer
      responses:
        '200':
          description: List of posts
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Post'
    post:
      summary: Create a new post
      description: Create a new blog post
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                  description: Post title
                body:
                  type: string
                  description: Post content
                userId:
                  type: integer
                  description: ID of the user creating the post
              required:
                - title
                - body
                - userId
      responses:
        '201':
          description: Post created successfully

  /posts/{id}:
    get:
      summary: Get a specific post
      description: Retrieve a single post by its ID
      parameters:
        - name: id
          in: path
          required: true
          description: Post ID
          schema:
            type: integer
      responses:
        '200':
          description: Post details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Post'

  /users:
    get:
      summary: Get all users
      description: Retrieve a list of all users
      responses:
        '200':
          description: List of users
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User'

  /users/{id}:
    get:
      summary: Get a specific user
      description: Retrieve a single user by their ID
      parameters:
        - name: id
          in: path
          required: true
          description: User ID
          schema:
            type: integer
      responses:
        '200':
          description: User details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'

  /comments:
    get:
      summary: Get comments
      description: Retrieve comments, optionally filtered by post
      parameters:
        - name: postId
          in: query
          description: Filter comments by post ID
          schema:
            type: integer
      responses:
        '200':
          description: List of comments

components:
  schemas:
    Post:
      type: object
      properties:
        id:
          type: integer
        title:
          type: string
        body:
          type: string
        userId:
          type: integer
    
    User:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        username:
          type: string
        email:
          type: string
        phone:
          type: string
        website:
          type: string
