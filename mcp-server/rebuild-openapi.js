#!/usr/bin/env node

import fs from 'fs';
import yaml from 'js-yaml';
import path from 'path';
import axios from 'axios';

// ElectronHub API configuration
const ELECTRONHUB_API_BASE = 'https://api.electronhub.ai/v1';
const ELECTRONHUB_API_KEY = 'ek-proxy-R4tBYVMxBMz7cH3dz3yfg5utVNaYuugooC4vQi2hB3hd';

const KNOWLEDGE_FOLDER = path.join(process.cwd(), 'openapi-knowledge');

// Clear and recreate knowledge folder
function clearKnowledgeFolder() {
  if (fs.existsSync(KNOWLEDGE_FOLDER)) {
    fs.rmSync(KNOWLEDGE_FOLDER, { recursive: true, force: true });
    console.log('🗑️  Cleared OpenAPI knowledge folder');
  }
  fs.mkdirSync(KNOWLEDGE_FOLDER, { recursive: true });
  console.log('📁 Created OpenAPI knowledge folder');
}

// Save tools to knowledge folder as JavaScript functions
function saveToolsToKnowledge(tools, schema) {
  const toolsFile = path.join(KNOWLEDGE_FOLDER, 'tools.js');
  const schemaFile = path.join(KNOWLEDGE_FOLDER, 'schema.json');

  // Generate JavaScript file with tool functions
  const jsContent = generateToolsJavaScript(tools, schema);
  fs.writeFileSync(toolsFile, jsContent);
  fs.writeFileSync(schemaFile, JSON.stringify(schema, null, 2));

  console.log(`💾 Saved ${tools.length} tools as JavaScript functions`);
}

// Generate JavaScript file content with tool functions
function generateToolsJavaScript(tools, schema) {
  const baseUrl = schema.servers?.[0]?.url || 'http://localhost';

  let jsContent = `// Auto-generated MCP tools from OpenAPI schema
// Schema: ${schema.info?.title || 'Unknown API'}
// Generated: ${new Date().toISOString()}

import axios from 'axios';

const API_BASE_URL = '${baseUrl}';

// Tool definitions and handlers
export const tools = [
`;

  // Generate each tool as a JavaScript object with function
  for (const tool of tools) {
    jsContent += `  {
    name: '${tool.name}',
    description: '${tool.description.replace(/'/g, "\\'")}',
    parameters: ${JSON.stringify(tool.parameters, null, 6)},
    handler: async (args) => {
      try {
        const config = {
          method: '${tool.method.toLowerCase()}',
          url: \`\${API_BASE_URL}${tool.path}\`,
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'MCP-Server/1.0'
          }
        };

        // Handle path parameters
        let finalUrl = config.url;
        ${generatePathParameterCode(tool.path)}

        // Handle query parameters and request body
        if ('${tool.method.toUpperCase()}' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(\`/\${value}\`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: '${tool.method} ${tool.path}',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: '${tool.method} ${tool.path}',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
`;
  }

  jsContent += `];

// Export individual tool handlers for easy access
${tools.map(tool => `export const ${tool.name} = tools.find(t => t.name === '${tool.name}').handler;`).join('\n')}

export default tools;
`;

  return jsContent;
}

// Generate path parameter replacement code
function generatePathParameterCode(path) {
  const pathParams = path.match(/\{([^}]+)\}/g);
  if (!pathParams) return '';

  return pathParams.map(param => {
    const paramName = param.slice(1, -1); // Remove { }
    return `        if (args.${paramName}) {
          finalUrl = finalUrl.replace('${param}', encodeURIComponent(args.${paramName}));
        }`;
  }).join('\n');
}

// Generate dynamic tools from OpenAPI schema using AI
async function generateDynamicTools(openApiSchema) {
  try {
    // Simplify the schema for AI processing - only include essential parts
    const simplifiedSchema = {
      info: openApiSchema.info,
      servers: openApiSchema.servers,
      paths: {}
    };

    // Only include first 10 paths to avoid token limits
    const pathEntries = Object.entries(openApiSchema.paths || {}).slice(0, 10);
    for (const [path, pathData] of pathEntries) {
      simplifiedSchema.paths[path] = {};
      // Only include GET, POST, PUT, DELETE methods
      for (const method of ['get', 'post', 'put', 'delete']) {
        if (pathData[method]) {
          simplifiedSchema.paths[path][method] = {
            summary: pathData[method].summary,
            description: pathData[method].description,
            parameters: pathData[method].parameters,
            requestBody: pathData[method].requestBody
          };
        }
      }
    }

    const schemaAnalysisPrompt = `Analyze this OpenAPI schema and create MCP tools for the most useful endpoints.

OpenAPI Schema:
${JSON.stringify(simplifiedSchema, null, 2)}

For each useful endpoint, create a tool with:
1. A clear, descriptive name (snake_case)
2. A helpful description of what it does
3. Parameters based on the endpoint's parameters/requestBody
4. Only include the most commonly used endpoints (max 8 tools)

Return ONLY a JSON array of tools in this exact format:
[
  {
    "name": "tool_name",
    "description": "What this tool does",
    "method": "GET",
    "path": "/api/path",
    "parameters": {
      "type": "object",
      "properties": {
        "param_name": {
          "type": "string",
          "description": "Parameter description"
        }
      },
      "required": ["param_name"]
    }
  }
]`;

    console.log('🤖 Analyzing OpenAPI schema with AI...');

    const response = await axios.post(`${ELECTRONHUB_API_BASE}/chat/completions`, {
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: schemaAnalysisPrompt }],
      max_tokens: 1500,
      temperature: 0.1
    }, {
      headers: {
        'Authorization': `Bearer ${ELECTRONHUB_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    const aiResponse = response.data.choices[0]?.message?.content;
    console.log('🔍 AI Response:', aiResponse.substring(0, 500) + '...');

    // Try multiple parsing strategies
    let toolDefinitions = null;

    // Strategy 1: Look for JSON array
    const jsonMatch = aiResponse.match(/\[[\s\S]*\]/);
    if (jsonMatch) {
      try {
        toolDefinitions = JSON.parse(jsonMatch[0]);
      } catch (e) {
        console.log('❌ Failed to parse JSON array');
      }
    }

    // Strategy 2: Look for ```json code block
    if (!toolDefinitions) {
      const codeBlockMatch = aiResponse.match(/```json\s*([\s\S]*?)\s*```/);
      if (codeBlockMatch) {
        try {
          toolDefinitions = JSON.parse(codeBlockMatch[1]);
        } catch (e) {
          console.log('❌ Failed to parse JSON code block');
        }
      }
    }

    // Strategy 3: Create fallback tools based on schema
    if (!toolDefinitions) {
      console.log('⚠️  AI parsing failed, creating fallback tools...');
      toolDefinitions = createFallbackTools(openApiSchema);
    }

    if (toolDefinitions && Array.isArray(toolDefinitions)) {
      console.log(`🛠️  Generated ${toolDefinitions.length} tools from OpenAPI schema`);
      return toolDefinitions;
    } else {
      throw new Error('Could not generate valid tool definitions');
    }
  } catch (error) {
    console.error('❌ Error generating dynamic tools:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
    throw error;
  }
}

// Create fallback tools when AI parsing fails
function createFallbackTools(openApiSchema) {
  const tools = [];
  const paths = openApiSchema.paths || {};

  for (const [pathKey, pathValue] of Object.entries(paths)) {
    for (const [method, operation] of Object.entries(pathValue)) {
      if (['get', 'post', 'put', 'delete', 'patch'].includes(method.toLowerCase())) {
        const toolName = `${method}_${pathKey.replace(/[^a-zA-Z0-9]/g, '_').replace(/_+/g, '_').replace(/^_|_$/g, '')}`;

        const tool = {
          name: toolName,
          description: operation.summary || operation.description || `${method.toUpperCase()} ${pathKey}`,
          method: method.toUpperCase(),
          path: pathKey,
          parameters: {
            type: 'object',
            properties: {},
            required: []
          }
        };

        // Add path parameters
        if (operation.parameters) {
          for (const param of operation.parameters) {
            if (param.in === 'path' || param.in === 'query') {
              tool.parameters.properties[param.name] = {
                type: param.schema?.type || 'string',
                description: param.description || `${param.name} parameter`
              };
              if (param.required) {
                tool.parameters.required.push(param.name);
              }
            }
          }
        }

        // Add request body parameters for POST/PUT
        if (operation.requestBody && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
          const content = operation.requestBody.content;
          if (content && content['application/json'] && content['application/json'].schema) {
            const schema = content['application/json'].schema;
            if (schema.properties) {
              Object.assign(tool.parameters.properties, schema.properties);
              if (schema.required) {
                tool.parameters.required.push(...schema.required);
              }
            }
          }
        }

        tools.push(tool);

        // Limit to 10 tools
        if (tools.length >= 10) break;
      }
    }
    if (tools.length >= 10) break;
  }

  return tools;
}

async function main() {
  try {
    console.log('🔄 Rebuilding OpenAPI knowledge...');
    
    // Check if openapi.yml exists
    const schemaPath = path.join(process.cwd(), 'openapi.yml');
    if (!fs.existsSync(schemaPath)) {
      console.error('❌ No openapi.yml file found in current directory');
      process.exit(1);
    }
    
    // Load OpenAPI schema
    const schemaContent = fs.readFileSync(schemaPath, 'utf8');
    const openApiSchema = yaml.load(schemaContent);
    console.log('📋 Loaded OpenAPI schema:', openApiSchema.info?.title || 'Unknown API');
    
    // Clear knowledge folder
    clearKnowledgeFolder();
    
    // Generate tools using AI
    const toolDefinitions = await generateDynamicTools(openApiSchema);

    // Save to knowledge folder as JavaScript functions
    saveToolsToKnowledge(toolDefinitions, openApiSchema);
    
    console.log('✅ OpenAPI knowledge rebuilt successfully!');
    console.log(`📊 Generated ${toolDefinitions.length} tools for ${openApiSchema.info?.title || 'Unknown API'}`);
    
  } catch (error) {
    console.error('❌ Failed to rebuild OpenAPI knowledge:', error.message);
    process.exit(1);
  }
}

main();
