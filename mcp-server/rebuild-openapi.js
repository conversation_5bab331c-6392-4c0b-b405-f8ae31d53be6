#!/usr/bin/env node

import fs from 'fs';
import yaml from 'js-yaml';
import path from 'path';
import axios from 'axios';

// ElectronHub API configuration
const ELECTRONHUB_API_BASE = 'https://api.electronhub.ai/v1';
const ELECTRONHUB_API_KEY = 'ek-proxy-R4tBYVMxBMz7cH3dz3yfg5utVNaYuugooC4vQi2hB3hd';

const KNOWLEDGE_FOLDER = path.join(process.cwd(), 'openapi-knowledge');

// Clear and recreate knowledge folder
function clearKnowledgeFolder() {
  if (fs.existsSync(KNOWLEDGE_FOLDER)) {
    fs.rmSync(KNOWLEDGE_FOLDER, { recursive: true, force: true });
    console.log('🗑️  Cleared OpenAPI knowledge folder');
  }
  fs.mkdirSync(KNOWLEDGE_FOLDER, { recursive: true });
  console.log('📁 Created OpenAPI knowledge folder');
}

// Save tools to knowledge folder
function saveToolsToKnowledge(tools, schema) {
  const toolsFile = path.join(KNOWLEDGE_FOLDER, 'tools.json');
  const schemaFile = path.join(KNOWLEDGE_FOLDER, 'schema.json');
  
  fs.writeFileSync(toolsFile, JSON.stringify(tools, null, 2));
  fs.writeFileSync(schemaFile, JSON.stringify(schema, null, 2));
  
  console.log(`💾 Saved ${tools.length} tools to knowledge folder`);
}

// Generate dynamic tools from OpenAPI schema using AI
async function generateDynamicTools(openApiSchema) {
  try {
    // Use AI to analyze the OpenAPI schema and create tool descriptions
    const schemaAnalysisPrompt = `Analyze this OpenAPI schema and create MCP tools for the most useful endpoints.

OpenAPI Schema:
${JSON.stringify(openApiSchema, null, 2)}

For each useful endpoint, create a tool with:
1. A clear, descriptive name (snake_case)
2. A helpful description of what it does
3. Parameters based on the endpoint's parameters/requestBody
4. Only include the most commonly used endpoints (max 10 tools)

Return a JSON array of tools in this format:
[
  {
    "name": "tool_name",
    "description": "What this tool does",
    "method": "GET|POST|PUT|DELETE",
    "path": "/api/path",
    "parameters": {
      "type": "object",
      "properties": {
        "param_name": {
          "type": "string",
          "description": "Parameter description"
        }
      },
      "required": ["param_name"]
    }
  }
]`;

    console.log('🤖 Analyzing OpenAPI schema with AI...');
    
    const response = await axios.post(`${ELECTRONHUB_API_BASE}/chat/completions`, {
      model: 'gpt-4',
      messages: [{ role: 'user', content: schemaAnalysisPrompt }],
      max_tokens: 2000
    }, {
      headers: {
        'Authorization': `Bearer ${ELECTRONHUB_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    const aiResponse = response.data.choices[0]?.message?.content;
    console.log('🔍 AI Response:', aiResponse.substring(0, 500) + '...');

    // Try multiple parsing strategies
    let toolDefinitions = null;

    // Strategy 1: Look for JSON array
    const jsonMatch = aiResponse.match(/\[[\s\S]*\]/);
    if (jsonMatch) {
      try {
        toolDefinitions = JSON.parse(jsonMatch[0]);
      } catch (e) {
        console.log('❌ Failed to parse JSON array');
      }
    }

    // Strategy 2: Look for ```json code block
    if (!toolDefinitions) {
      const codeBlockMatch = aiResponse.match(/```json\s*([\s\S]*?)\s*```/);
      if (codeBlockMatch) {
        try {
          toolDefinitions = JSON.parse(codeBlockMatch[1]);
        } catch (e) {
          console.log('❌ Failed to parse JSON code block');
        }
      }
    }

    // Strategy 3: Create fallback tools based on schema
    if (!toolDefinitions) {
      console.log('⚠️  AI parsing failed, creating fallback tools...');
      toolDefinitions = createFallbackTools(openApiSchema);
    }

    if (toolDefinitions && Array.isArray(toolDefinitions)) {
      console.log(`🛠️  Generated ${toolDefinitions.length} tools from OpenAPI schema`);
      return toolDefinitions;
    } else {
      throw new Error('Could not generate valid tool definitions');
    }
  } catch (error) {
    console.error('❌ Error generating dynamic tools:', error.message);
    throw error;
  }
}

// Create fallback tools when AI parsing fails
function createFallbackTools(openApiSchema) {
  const tools = [];
  const paths = openApiSchema.paths || {};

  for (const [pathKey, pathValue] of Object.entries(paths)) {
    for (const [method, operation] of Object.entries(pathValue)) {
      if (['get', 'post', 'put', 'delete', 'patch'].includes(method.toLowerCase())) {
        const toolName = `${method}_${pathKey.replace(/[^a-zA-Z0-9]/g, '_').replace(/_+/g, '_').replace(/^_|_$/g, '')}`;

        const tool = {
          name: toolName,
          description: operation.summary || operation.description || `${method.toUpperCase()} ${pathKey}`,
          method: method.toUpperCase(),
          path: pathKey,
          parameters: {
            type: 'object',
            properties: {},
            required: []
          }
        };

        // Add path parameters
        if (operation.parameters) {
          for (const param of operation.parameters) {
            if (param.in === 'path' || param.in === 'query') {
              tool.parameters.properties[param.name] = {
                type: param.schema?.type || 'string',
                description: param.description || `${param.name} parameter`
              };
              if (param.required) {
                tool.parameters.required.push(param.name);
              }
            }
          }
        }

        // Add request body parameters for POST/PUT
        if (operation.requestBody && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
          const content = operation.requestBody.content;
          if (content && content['application/json'] && content['application/json'].schema) {
            const schema = content['application/json'].schema;
            if (schema.properties) {
              Object.assign(tool.parameters.properties, schema.properties);
              if (schema.required) {
                tool.parameters.required.push(...schema.required);
              }
            }
          }
        }

        tools.push(tool);

        // Limit to 10 tools
        if (tools.length >= 10) break;
      }
    }
    if (tools.length >= 10) break;
  }

  return tools;
}

async function main() {
  try {
    console.log('🔄 Rebuilding OpenAPI knowledge...');
    
    // Check if openapi.yml exists
    const schemaPath = path.join(process.cwd(), 'openapi.yml');
    if (!fs.existsSync(schemaPath)) {
      console.error('❌ No openapi.yml file found in current directory');
      process.exit(1);
    }
    
    // Load OpenAPI schema
    const schemaContent = fs.readFileSync(schemaPath, 'utf8');
    const openApiSchema = yaml.load(schemaContent);
    console.log('📋 Loaded OpenAPI schema:', openApiSchema.info?.title || 'Unknown API');
    
    // Clear knowledge folder
    clearKnowledgeFolder();
    
    // Generate tools using AI
    const toolDefinitions = await generateDynamicTools(openApiSchema);
    
    // Save to knowledge folder
    saveToolsToKnowledge(toolDefinitions, openApiSchema);
    
    console.log('✅ OpenAPI knowledge rebuilt successfully!');
    console.log(`📊 Generated ${toolDefinitions.length} tools for ${openApiSchema.info?.title || 'Unknown API'}`);
    
  } catch (error) {
    console.error('❌ Failed to rebuild OpenAPI knowledge:', error.message);
    process.exit(1);
  }
}

main();
