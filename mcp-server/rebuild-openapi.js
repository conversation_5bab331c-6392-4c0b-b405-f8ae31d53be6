#!/usr/bin/env node

import fs from 'fs';
import yaml from 'js-yaml';
import path from 'path';
import axios from 'axios';

// ElectronHub API configuration
const ELECTRONHUB_API_BASE = 'https://api.electronhub.ai/v1';
const ELECTRONHUB_API_KEY = 'ek-proxy-R4tBYVMxBMz7cH3dz3yfg5utVNaYuugooC4vQi2hB3hd';

const KNOWLEDGE_FOLDER = path.join(process.cwd(), 'openapi-knowledge');

// Clear and recreate knowledge folder
function clearKnowledgeFolder() {
  if (fs.existsSync(KNOWLEDGE_FOLDER)) {
    fs.rmSync(KNOWLEDGE_FOLDER, { recursive: true, force: true });
    console.log('🗑️  Cleared OpenAPI knowledge folder');
  }
  fs.mkdirSync(KNOWLEDGE_FOLDER, { recursive: true });
  console.log('📁 Created OpenAPI knowledge folder');
}

// Save tools to knowledge folder as JavaScript functions
function saveToolsToKnowledge(tools, schema) {
  const toolsFile = path.join(KNOWLEDGE_FOLDER, 'tools.js');
  const schemaFile = path.join(KNOWLEDGE_FOLDER, 'schema.json');

  // Generate JavaScript file with tool functions
  const jsContent = generateToolsJavaScript(tools, schema);
  fs.writeFileSync(toolsFile, jsContent);
  fs.writeFileSync(schemaFile, JSON.stringify(schema, null, 2));

  console.log(`💾 Saved ${tools.length} tools as JavaScript functions`);
}

// Generate JavaScript file content with tool functions
function generateToolsJavaScript(tools, schema) {
  const baseUrl = schema.servers?.[0]?.url || 'http://localhost';
  const security = extractSecurityInfo(schema);

  let jsContent = `// Auto-generated MCP tools from OpenAPI schema
// Schema: ${schema.info?.title || 'Unknown API'}
// Generated: ${new Date().toISOString()}

import axios from 'axios';

const API_BASE_URL = '${baseUrl}';

// Authentication configuration
// Set these environment variables or modify the values below:
${generateAuthConfig(security)}

// Helper function to get authentication headers
function getAuthHeaders() {
  const headers = {
    'Content-Type': 'application/json',
    'User-Agent': 'MCP-Server/1.0'
  };

${generateAuthHeadersCode(security)}

  return headers;
}

// Tool definitions and handlers
export const tools = [
`;

  // Generate each tool as a JavaScript object with function
  for (const tool of tools) {
    jsContent += `  {
    name: '${tool.name}',
    description: '${tool.description.replace(/'/g, "\\'")}',
    parameters: ${JSON.stringify(tool.parameters, null, 6)},
    handler: async (args) => {
      try {
        const config = {
          method: '${tool.method.toLowerCase()}',
          url: \`\${API_BASE_URL}${tool.path}\`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
        ${generatePathParameterCode(tool.path)}

        // Handle query parameters and request body
        if ('${tool.method.toUpperCase()}' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(\`/\${value}\`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: '${tool.method} ${tool.path}',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: '${tool.method} ${tool.path}',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
`;
  }

  jsContent += `];

// Export individual tool handlers for easy access
${tools.map(tool => `export const ${tool.name} = tools.find(t => t.name === '${tool.name}').handler;`).join('\n')}

export default tools;
`;

  return jsContent;
}

// Generate authentication configuration
function generateAuthConfig(security) {
  if (!security || Object.keys(security.schemes).length === 0) {
    return '// No authentication required for this API';
  }

  let config = '';
  for (const [name, scheme] of Object.entries(security.schemes)) {
    if (scheme.type === 'http' && scheme.scheme === 'bearer') {
      config += `const ${name.toUpperCase()}_TOKEN = process.env.${name.toUpperCase()}_TOKEN || ''; // Bearer token\n`;
    } else if (scheme.type === 'apiKey') {
      config += `const ${name.toUpperCase()}_KEY = process.env.${name.toUpperCase()}_KEY || ''; // API key\n`;
    }
  }

  return config;
}

// Generate authentication headers code
function generateAuthHeadersCode(security) {
  if (!security || Object.keys(security.schemes).length === 0) {
    return '  // No authentication required';
  }

  let code = '';
  for (const [name, scheme] of Object.entries(security.schemes)) {
    if (scheme.type === 'http' && scheme.scheme === 'bearer') {
      code += `  if (${name.toUpperCase()}_TOKEN) {\n`;
      code += `    headers['Authorization'] = \`Bearer \${${name.toUpperCase()}_TOKEN}\`;\n`;
      code += `  }\n`;
    } else if (scheme.type === 'apiKey') {
      if (scheme.in === 'header') {
        code += `  if (${name.toUpperCase()}_KEY) {\n`;
        code += `    headers['${scheme.name}'] = ${name.toUpperCase()}_KEY;\n`;
        code += `  }\n`;
      }
    }
  }

  return code || '  // No authentication configured';
}

// Generate path parameter replacement code
function generatePathParameterCode(path) {
  const pathParams = path.match(/\{([^}]+)\}/g);
  if (!pathParams) return '';

  return pathParams.map(param => {
    const paramName = param.slice(1, -1); // Remove { }
    return `        if (args.${paramName}) {
          finalUrl = finalUrl.replace('${param}', encodeURIComponent(args.${paramName}));
        }`;
  }).join('\n');
}

// Analyze OpenAPI schema and extract key information
function analyzeOpenApiSchema(openApiSchema) {
  const analysis = {
    info: openApiSchema.info,
    servers: openApiSchema.servers,
    security: extractSecurityInfo(openApiSchema),
    endpoints: [],
    totalPaths: Object.keys(openApiSchema.paths || {}).length
  };

  // Extract and prioritize endpoints
  const pathEntries = Object.entries(openApiSchema.paths || {});

  for (const [path, pathData] of pathEntries) {
    for (const method of ['get', 'post', 'put', 'delete', 'patch']) {
      if (pathData[method]) {
        const endpoint = {
          path,
          method: method.toUpperCase(),
          summary: pathData[method].summary || `${method.toUpperCase()} ${path}`,
          description: truncateDescription(pathData[method].description),
          operationId: pathData[method].operationId,
          tags: pathData[method].tags || [],
          parameters: extractParameters(pathData[method].parameters),
          requestBody: extractRequestBody(pathData[method].requestBody),
          security: pathData[method].security || openApiSchema.security,
          priority: calculateEndpointPriority(path, method, pathData[method])
        };
        analysis.endpoints.push(endpoint);
      }
    }
  }

  // Sort by priority and limit to most important endpoints
  analysis.endpoints.sort((a, b) => b.priority - a.priority);
  analysis.endpoints = analysis.endpoints.slice(0, 15); // Limit to 15 most important

  return analysis;
}

// Extract security information from schema
function extractSecurityInfo(schema) {
  const security = {
    schemes: {},
    globalRequirements: schema.security || []
  };

  if (schema.components?.securitySchemes) {
    for (const [name, scheme] of Object.entries(schema.components.securitySchemes)) {
      security.schemes[name] = {
        type: scheme.type,
        scheme: scheme.scheme,
        bearerFormat: scheme.bearerFormat,
        in: scheme.in,
        name: scheme.name,
        description: truncateDescription(scheme.description)
      };
    }
  }

  return security;
}

// Calculate endpoint priority based on common patterns
function calculateEndpointPriority(path, method, operation) {
  let priority = 0;

  // Higher priority for common operations
  if (method === 'get') priority += 3;
  if (method === 'post') priority += 2;
  if (method === 'put' || method === 'patch') priority += 1;

  // Higher priority for shorter paths (usually more general)
  priority += Math.max(0, 5 - path.split('/').length);

  // Higher priority for operations with clear summaries
  if (operation.summary && operation.summary.length > 5) priority += 2;

  // Higher priority for list/search operations
  if (path.includes('search') || path.includes('find') || path.includes('list')) priority += 3;

  // Lower priority for very specific operations
  if (path.includes('{id}') && method !== 'get') priority -= 1;

  return priority;
}

// Truncate long descriptions to save tokens
function truncateDescription(description, maxLength = 100) {
  if (!description) return '';
  if (description.length <= maxLength) return description;
  return description.substring(0, maxLength) + '...';
}

// Extract and simplify parameters
function extractParameters(parameters) {
  if (!parameters) return [];

  return parameters.map(param => ({
    name: param.name,
    in: param.in,
    required: param.required || false,
    type: param.schema?.type || 'string',
    description: truncateDescription(param.description, 50)
  }));
}

// Extract and simplify request body
function extractRequestBody(requestBody) {
  if (!requestBody) return null;

  const content = requestBody.content;
  if (content && content['application/json']) {
    const schema = content['application/json'].schema;
    return {
      required: requestBody.required || false,
      properties: extractSchemaProperties(schema)
    };
  }

  return null;
}

// Extract schema properties with limits
function extractSchemaProperties(schema, depth = 0) {
  if (!schema || depth > 2) return {};

  const properties = {};
  if (schema.properties) {
    // Limit to 10 properties to save tokens
    const propEntries = Object.entries(schema.properties).slice(0, 10);
    for (const [name, prop] of propEntries) {
      properties[name] = {
        type: prop.type || 'string',
        description: truncateDescription(prop.description, 30),
        required: schema.required?.includes(name) || false
      };
    }
  }

  return properties;
}

// Generate dynamic tools from OpenAPI schema using AI with chunking
async function generateDynamicTools(openApiSchema) {
  try {
    console.log('🔍 Analyzing OpenAPI schema...');
    const analysis = analyzeOpenApiSchema(openApiSchema);

    console.log(`📊 Found ${analysis.totalPaths} total paths, processing ${analysis.endpoints.length} most important endpoints`);

    // Process endpoints in chunks to avoid token limits
    const chunkSize = 8;
    const chunks = [];
    for (let i = 0; i < analysis.endpoints.length; i += chunkSize) {
      chunks.push(analysis.endpoints.slice(i, i + chunkSize));
    }

    let allTools = [];

    for (let i = 0; i < chunks.length; i++) {
      console.log(`🤖 Processing chunk ${i + 1}/${chunks.length} with AI...`);
      const chunkTools = await processEndpointChunk(chunks[i], analysis);
      allTools = allTools.concat(chunkTools);

      // Add delay between chunks to avoid rate limits
      if (i < chunks.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    console.log(`🛠️  Generated ${allTools.length} tools from OpenAPI schema`);
    return allTools;

  } catch (error) {
    console.error('❌ Error generating dynamic tools:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
    throw error;
  }
}

// Process a chunk of endpoints with AI
async function processEndpointChunk(endpoints, analysis) {
  const prompt = `Create MCP tools for these API endpoints. Focus on practical, usable tools.

API Info: ${analysis.info?.title || 'API'} - ${analysis.info?.description || ''}
Base URL: ${analysis.servers?.[0]?.url || 'https://api.example.com'}

Security Requirements:
${formatSecurityInfo(analysis.security)}

Endpoints to process:
${endpoints.map(ep => `${ep.method} ${ep.path} - ${ep.summary}`).join('\n')}

Detailed endpoint info:
${JSON.stringify(endpoints, null, 2)}

Create a tool for each endpoint with:
1. Clear snake_case name based on operation
2. Helpful description (what it does, not how)
3. Proper parameters from path/query/body
4. Mark required parameters correctly

Return ONLY a JSON array:
[
  {
    "name": "descriptive_tool_name",
    "description": "What this tool accomplishes",
    "method": "GET|POST|PUT|DELETE",
    "path": "/api/path",
    "parameters": {
      "type": "object",
      "properties": {
        "param_name": {
          "type": "string|number|boolean",
          "description": "Brief param description"
        }
      },
      "required": ["required_param"]
    }
  }
]`;

  try {
    const response = await axios.post(`${ELECTRONHUB_API_BASE}/chat/completions`, {
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: prompt }],
      max_tokens: 2000,
      temperature: 0.1
    }, {
      headers: {
        'Authorization': `Bearer ${ELECTRONHUB_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    const aiResponse = response.data.choices[0]?.message?.content;

    // Parse AI response with multiple strategies
    let toolDefinitions = parseAIResponse(aiResponse);

    if (!toolDefinitions) {
      console.log('⚠️  AI parsing failed for chunk, creating fallback tools...');
      toolDefinitions = createFallbackToolsFromEndpoints(endpoints);
    }

    return toolDefinitions || [];
  } catch (error) {
    console.error('❌ Error processing chunk:', error.message);
    // Return fallback tools for this chunk
    return createFallbackToolsFromEndpoints(endpoints);
  }
}

// Format security information for AI prompt
function formatSecurityInfo(security) {
  if (!security || Object.keys(security.schemes).length === 0) {
    return 'No authentication required';
  }

  let securityText = '';
  for (const [name, scheme] of Object.entries(security.schemes)) {
    securityText += `${name}: `;
    if (scheme.type === 'http' && scheme.scheme === 'bearer') {
      securityText += `Bearer token required (${scheme.bearerFormat || 'JWT'})`;
    } else if (scheme.type === 'apiKey') {
      securityText += `API key in ${scheme.in} parameter '${scheme.name}'`;
    } else if (scheme.type === 'oauth2') {
      securityText += 'OAuth2 authentication required';
    } else {
      securityText += `${scheme.type} authentication`;
    }
    securityText += '\n';
  }

  return securityText;
}

// Parse AI response with multiple strategies
function parseAIResponse(aiResponse) {
  if (!aiResponse) return null;

  // Strategy 1: Look for JSON array
  const jsonMatch = aiResponse.match(/\[[\s\S]*\]/);
  if (jsonMatch) {
    try {
      return JSON.parse(jsonMatch[0]);
    } catch (e) {
      console.log('❌ Failed to parse JSON array');
    }
  }

  // Strategy 2: Look for ```json code block
  const codeBlockMatch = aiResponse.match(/```json\s*([\s\S]*?)\s*```/);
  if (codeBlockMatch) {
    try {
      return JSON.parse(codeBlockMatch[1]);
    } catch (e) {
      console.log('❌ Failed to parse JSON code block');
    }
  }

  // Strategy 3: Look for individual tool objects
  const toolMatches = aiResponse.match(/\{[^{}]*"name"[^{}]*\}/g);
  if (toolMatches) {
    try {
      const tools = toolMatches.map(match => JSON.parse(match));
      return tools;
    } catch (e) {
      console.log('❌ Failed to parse individual tools');
    }
  }

  return null;
}

// Create fallback tools from endpoints when AI fails
function createFallbackToolsFromEndpoints(endpoints) {
  return endpoints.map(endpoint => {
    const toolName = `${endpoint.method.toLowerCase()}_${endpoint.path.replace(/[^a-zA-Z0-9]/g, '_').replace(/_+/g, '_').replace(/^_|_$/g, '')}`;

    const parameters = {
      type: 'object',
      properties: {},
      required: []
    };

    // Add path parameters
    endpoint.parameters?.forEach(param => {
      if (param.in === 'path' || param.in === 'query') {
        parameters.properties[param.name] = {
          type: param.type,
          description: param.description || `${param.name} parameter`
        };
        if (param.required) {
          parameters.required.push(param.name);
        }
      }
    });

    // Add request body parameters
    if (endpoint.requestBody?.properties) {
      Object.assign(parameters.properties, endpoint.requestBody.properties);
      if (endpoint.requestBody.required) {
        parameters.required.push(...Object.keys(endpoint.requestBody.properties));
      }
    }

    return {
      name: toolName,
      description: endpoint.summary || `${endpoint.method} ${endpoint.path}`,
      method: endpoint.method,
      path: endpoint.path,
      parameters
    };
  });
}

// Create fallback tools when AI parsing fails
function createFallbackTools(openApiSchema) {
  const tools = [];
  const paths = openApiSchema.paths || {};

  for (const [pathKey, pathValue] of Object.entries(paths)) {
    for (const [method, operation] of Object.entries(pathValue)) {
      if (['get', 'post', 'put', 'delete', 'patch'].includes(method.toLowerCase())) {
        const toolName = `${method}_${pathKey.replace(/[^a-zA-Z0-9]/g, '_').replace(/_+/g, '_').replace(/^_|_$/g, '')}`;

        const tool = {
          name: toolName,
          description: operation.summary || operation.description || `${method.toUpperCase()} ${pathKey}`,
          method: method.toUpperCase(),
          path: pathKey,
          parameters: {
            type: 'object',
            properties: {},
            required: []
          }
        };

        // Add path parameters
        if (operation.parameters) {
          for (const param of operation.parameters) {
            if (param.in === 'path' || param.in === 'query') {
              tool.parameters.properties[param.name] = {
                type: param.schema?.type || 'string',
                description: param.description || `${param.name} parameter`
              };
              if (param.required) {
                tool.parameters.required.push(param.name);
              }
            }
          }
        }

        // Add request body parameters for POST/PUT
        if (operation.requestBody && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
          const content = operation.requestBody.content;
          if (content && content['application/json'] && content['application/json'].schema) {
            const schema = content['application/json'].schema;
            if (schema.properties) {
              Object.assign(tool.parameters.properties, schema.properties);
              if (schema.required) {
                tool.parameters.required.push(...schema.required);
              }
            }
          }
        }

        tools.push(tool);

        // Limit to 10 tools
        if (tools.length >= 10) break;
      }
    }
    if (tools.length >= 10) break;
  }

  return tools;
}

async function main() {
  try {
    console.log('🔄 Rebuilding OpenAPI knowledge...');
    
    // Check if openapi.yml exists
    const schemaPath = path.join(process.cwd(), 'openapi.yml');
    if (!fs.existsSync(schemaPath)) {
      console.error('❌ No openapi.yml file found in current directory');
      process.exit(1);
    }
    
    // Load OpenAPI schema
    const schemaContent = fs.readFileSync(schemaPath, 'utf8');
    const openApiSchema = yaml.load(schemaContent);
    console.log('📋 Loaded OpenAPI schema:', openApiSchema.info?.title || 'Unknown API');
    
    // Clear knowledge folder
    clearKnowledgeFolder();
    
    // Generate tools using AI
    const toolDefinitions = await generateDynamicTools(openApiSchema);

    // Save to knowledge folder as JavaScript functions
    saveToolsToKnowledge(toolDefinitions, openApiSchema);
    
    console.log('✅ OpenAPI knowledge rebuilt successfully!');
    console.log(`📊 Generated ${toolDefinitions.length} tools for ${openApiSchema.info?.title || 'Unknown API'}`);
    
  } catch (error) {
    console.error('❌ Failed to rebuild OpenAPI knowledge:', error.message);
    process.exit(1);
  }
}

main();
