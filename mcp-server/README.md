# Dynamic MCP Server

MCP server that dynamically generates tools from OpenAPI schemas using AI.

## How It Works

1. **Place OpenAPI schema**: Put your `openapi.yml` file in the server directory
2. **AI Analysis**: Server uses AI to analyze the schema and create relevant tools
3. **Dynamic Tools**: Tools are automatically generated for the most useful API endpoints
4. **Fallback**: If no schema is found, defaults to basic AI tool

## Setup

```bash
npm install
npm start
```

Server runs on port 3000.

## OpenAPI Schema

Create an `openapi.yml` file in the server directory. The AI will analyze it and create tools automatically.

Example schema structure:
```yaml
openapi: 3.0.0
info:
  title: My API
  version: 1.0.0
servers:
  - url: https://api.example.com
paths:
  /users:
    get:
      summary: Get users
      # ... rest of your OpenAPI spec
```

## Rebuild OpenAPI Knowledge

To rebuild the tools from your OpenAPI schema:

```bash
npm run rebuild-openapi
```

This will:
1. Clear the `openapi-knowledge/` folder
2. Analyze your `openapi.yml` with AI
3. Generate new tools and save them
4. Cache the results for faster startup

## API Endpoints

- `GET /tools` - List dynamically generated tools
- `POST /tools/call` - Call a tool
- `GET /health` - Health check + schema info
- `POST /reload-schema` - Reload OpenAPI schema
- `POST /rebuild-openapi` - Force rebuild knowledge from scratch

## Example

```bash
# Check what tools are available
curl http://localhost:3000/tools

# Call a tool
curl -X POST http://localhost:3000/tools/call \
  -H "Content-Type: application/json" \
  -d '{"name": "ask_ai", "arguments": {"question": "Hello"}}'

# Reload schema after changes
curl -X POST http://localhost:3000/reload-schema
```
