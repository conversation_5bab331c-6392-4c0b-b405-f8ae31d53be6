# MCP Server

Simple MCP server with basic tools.

## Tools Available

- `get_weather` - Get weather for a location
- `calculate` - Do math calculations  
- `ask_ai` - Ask AI questions (uses ElectronHub)

## Setup

```bash
npm install
npm start
```

Server runs on port 3000.

## API

- `GET /tools` - List available tools
- `POST /tools/call` - Call a tool
- `GET /health` - Health check

## Example Tool Call

```bash
curl -X POST http://localhost:3000/tools/call \
  -H "Content-Type: application/json" \
  -d '{"name": "get_weather", "arguments": {"location": "Paris"}}'
```
