// Auto-generated MCP tools from OpenAPI schema
// Schema: Swagger Petstore - OpenAPI 3.0
// Generated: 2025-06-25T18:10:40.160Z

import axios from 'axios';

const API_BASE_URL = 'https://petstore3.swagger.io/api/v3';

// Tool definitions and handlers
export const tools = [
  {
    name: 'add_new_pet',
    description: 'Add a new pet to the store',
    parameters: {
      "type": "object",
      "properties": {
            "name": {
                  "type": "string",
                  "description": "Name of the pet"
            },
            "status": {
                  "type": "string",
                  "description": "Status of the pet"
            }
      },
      "required": [
            "name",
            "status"
      ]
},
    handler: async (args) => {
      try {
        const config = {
          method: 'post',
          url: `${API_BASE_URL}/pet`,
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'MCP-Server/1.0'
          }
        };

        // Handle path parameters
        let finalUrl = config.url;
        

        // Handle query parameters and request body
        if ('POST' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'POST /pet',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'POST /pet',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'update_existing_pet',
    description: 'Update an existing pet by ID',
    parameters: {
      "type": "object",
      "properties": {
            "petId": {
                  "type": "integer",
                  "description": "ID of the pet to update"
            },
            "name": {
                  "type": "string",
                  "description": "Name of the pet"
            },
            "status": {
                  "type": "string",
                  "description": "Status of the pet"
            }
      },
      "required": [
            "petId",
            "name",
            "status"
      ]
},
    handler: async (args) => {
      try {
        const config = {
          method: 'put',
          url: `${API_BASE_URL}/pet`,
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'MCP-Server/1.0'
          }
        };

        // Handle path parameters
        let finalUrl = config.url;
        

        // Handle query parameters and request body
        if ('PUT' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'PUT /pet',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'PUT /pet',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'find_pets_by_status',
    description: 'Finds Pets by status',
    parameters: {
      "type": "object",
      "properties": {
            "status": {
                  "type": "string",
                  "description": "Status values to filter by"
            }
      },
      "required": []
},
    handler: async (args) => {
      try {
        const config = {
          method: 'get',
          url: `${API_BASE_URL}/pet/findByStatus`,
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'MCP-Server/1.0'
          }
        };

        // Handle path parameters
        let finalUrl = config.url;
        

        // Handle query parameters and request body
        if ('GET' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'GET /pet/findByStatus',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'GET /pet/findByStatus',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'find_pets_by_tags',
    description: 'Finds Pets by tags',
    parameters: {
      "type": "object",
      "properties": {
            "tags": {
                  "type": "array",
                  "items": {
                        "type": "string"
                  },
                  "description": "Tags to filter by"
            }
      },
      "required": []
},
    handler: async (args) => {
      try {
        const config = {
          method: 'get',
          url: `${API_BASE_URL}/pet/findByTags`,
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'MCP-Server/1.0'
          }
        };

        // Handle path parameters
        let finalUrl = config.url;
        

        // Handle query parameters and request body
        if ('GET' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'GET /pet/findByTags',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'GET /pet/findByTags',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'find_pet_by_id',
    description: 'Find pet by ID',
    parameters: {
      "type": "object",
      "properties": {
            "petId": {
                  "type": "integer",
                  "description": "ID of the pet to return"
            }
      },
      "required": [
            "petId"
      ]
},
    handler: async (args) => {
      try {
        const config = {
          method: 'get',
          url: `${API_BASE_URL}/pet/{petId}`,
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'MCP-Server/1.0'
          }
        };

        // Handle path parameters
        let finalUrl = config.url;
                if (args.petId) {
          finalUrl = finalUrl.replace('{petId}', encodeURIComponent(args.petId));
        }

        // Handle query parameters and request body
        if ('GET' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'GET /pet/{petId}',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'GET /pet/{petId}',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'update_pet_with_form_data',
    description: 'Updates a pet in the store with form data',
    parameters: {
      "type": "object",
      "properties": {
            "petId": {
                  "type": "integer",
                  "description": "ID of the pet to update"
            },
            "name": {
                  "type": "string",
                  "description": "Name of the pet"
            },
            "status": {
                  "type": "string",
                  "description": "Status of the pet"
            }
      },
      "required": [
            "petId",
            "name",
            "status"
      ]
},
    handler: async (args) => {
      try {
        const config = {
          method: 'post',
          url: `${API_BASE_URL}/pet/{petId}`,
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'MCP-Server/1.0'
          }
        };

        // Handle path parameters
        let finalUrl = config.url;
                if (args.petId) {
          finalUrl = finalUrl.replace('{petId}', encodeURIComponent(args.petId));
        }

        // Handle query parameters and request body
        if ('POST' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'POST /pet/{petId}',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'POST /pet/{petId}',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'delete_pet',
    description: 'Delete a pet',
    parameters: {
      "type": "object",
      "properties": {
            "petId": {
                  "type": "integer",
                  "description": "ID of the pet to delete"
            }
      },
      "required": [
            "petId"
      ]
},
    handler: async (args) => {
      try {
        const config = {
          method: 'delete',
          url: `${API_BASE_URL}/pet/{petId}`,
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'MCP-Server/1.0'
          }
        };

        // Handle path parameters
        let finalUrl = config.url;
                if (args.petId) {
          finalUrl = finalUrl.replace('{petId}', encodeURIComponent(args.petId));
        }

        // Handle query parameters and request body
        if ('DELETE' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'DELETE /pet/{petId}',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'DELETE /pet/{petId}',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'place_order_for_pet',
    description: 'Place an order for a pet',
    parameters: {
      "type": "object",
      "properties": {
            "petId": {
                  "type": "integer",
                  "description": "ID of the pet to order"
            },
            "quantity": {
                  "type": "integer",
                  "description": "Quantity of the pet"
            },
            "shipDate": {
                  "type": "string",
                  "description": "Shipping date"
            },
            "status": {
                  "type": "string",
                  "description": "Order status"
            },
            "complete": {
                  "type": "boolean",
                  "description": "Order completion status"
            }
      },
      "required": [
            "petId",
            "quantity",
            "shipDate",
            "status",
            "complete"
      ]
},
    handler: async (args) => {
      try {
        const config = {
          method: 'post',
          url: `${API_BASE_URL}/store/order`,
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'MCP-Server/1.0'
          }
        };

        // Handle path parameters
        let finalUrl = config.url;
        

        // Handle query parameters and request body
        if ('POST' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'POST /store/order',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'POST /store/order',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
];

// Export individual tool handlers for easy access
export const add_new_pet = tools.find(t => t.name === 'add_new_pet').handler;
export const update_existing_pet = tools.find(t => t.name === 'update_existing_pet').handler;
export const find_pets_by_status = tools.find(t => t.name === 'find_pets_by_status').handler;
export const find_pets_by_tags = tools.find(t => t.name === 'find_pets_by_tags').handler;
export const find_pet_by_id = tools.find(t => t.name === 'find_pet_by_id').handler;
export const update_pet_with_form_data = tools.find(t => t.name === 'update_pet_with_form_data').handler;
export const delete_pet = tools.find(t => t.name === 'delete_pet').handler;
export const place_order_for_pet = tools.find(t => t.name === 'place_order_for_pet').handler;

export default tools;
