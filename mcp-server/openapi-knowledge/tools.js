// Auto-generated MCP tools from OpenAPI schema
// Schema: Autodesk Vault API (Mock)
// Generated: 2025-06-25T19:02:54.629Z

import axios from 'axios';

const API_BASE_URL = 'http://localhost:4000/AutodeskDM/Services/api/vault/v2';

// Authentication configuration
// Set these environment variables or modify the values below:
const BEARERAUTH_TOKEN = process.env.BEARERAUTH_TOKEN || 'test-token-12345'; // Bearer token


// Helper function to get authentication headers
function getAuthHeaders() {
  const headers = {
    'Content-Type': 'application/json',
    'User-Agent': 'MCP-Server/1.0'
  };

  if (BEARERAUTH_TOKEN) {
    headers['Authorization'] = `Bearer ${BEARERAUTH_TOKEN}`;
  }


  return headers;
}

// Tool definitions and handlers
export const tools = [
  {
    name: 'get_item_versions_in_vault',
    description: 'Retrieve item versions in a specific vault',
    parameters: {
      "type": "object",
      "properties": {
        "vaultId": {
          "type": "string",
          "description": "The unique identifier of a vault"
        },
        "q": {
          "type": "string",
          "description": "Search query across all properties (e.g., 'Assembly')"
        },
        "option[propDefIds]": {
          "type": "string",
          "description": "Properties to return (comma-separated IDs or 'all')"
        },
        "option[releasedItemsOnly]": {
          "type": "boolean",
          "description": "Include only released versions (default: false)"
        },
        "option[latestOnly]": {
          "type": "boolean",
          "description": "Include only latest versions (default: true)"
        },
        "sort": {
          "type": "string",
          "description": "Sort criteria (e.g., 'Revision desc,Name asc')"
        },
        "limit": {
          "type": "integer",
          "description": "Number of results per page (max 1000)"
        },
        "cursorState": {
          "type": "string",
          "description": "Cursor state for pagination"
        }
      },
      "required": ["vaultId"]
    },
    handler: async (args) => {
      try {
        const config = {
          method: 'get',
          url: `${API_BASE_URL}/vaults/{vaultId}/item-versions`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
                if (args.vaultId) {
          finalUrl = finalUrl.replace('{vaultId}', encodeURIComponent(args.vaultId));
        }

        // Handle query parameters and request body
        if ('GET' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            // Skip path parameters and empty values
            if (key !== 'vaultId' && value !== undefined && value !== null && value !== '') {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        console.log('🔧 Making request to:', config.url);
        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'GET /AutodeskDM/Services/api/vault/v2/vaults/{vaultId}/item-versions',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'GET /AutodeskDM/Services/api/vault/v2/vaults/{vaultId}/item-versions',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
];

// Export individual tool handlers for easy access
export const get_item_versions_in_vault = tools.find(t => t.name === 'get_item_versions_in_vault').handler;

export default tools;
