// Auto-generated MCP tools from OpenAPI schema
// Schema: Swagger Petstore - OpenAPI 3.0
// Generated: 2025-06-25T18:17:11.779Z

import axios from 'axios';

const API_BASE_URL = 'https://petstore3.swagger.io/api/v3';

// Authentication configuration
// Set these environment variables or modify the values below:
const API_KEY_KEY = process.env.API_KEY_KEY || ''; // API key


// Helper function to get authentication headers
function getAuthHeaders() {
  const headers = {
    'Content-Type': 'application/json',
    'User-Agent': 'MCP-Server/1.0'
  };

  if (API_KEY_KEY) {
    headers['api_key'] = API_KEY_KEY;
  }


  return headers;
}

// Tool definitions and handlers
export const tools = [
  {
    name: 'find_pets_by_status',
    description: 'Finds pets by status',
    parameters: {
      "type": "object",
      "properties": {
            "status": {
                  "type": "string",
                  "description": "Status values to filter by"
            }
      },
      "required": []
},
    handler: async (args) => {
      try {
        const config = {
          method: 'get',
          url: `${API_BASE_URL}/pet/findByStatus`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
        

        // Handle query parameters and request body
        if ('GET' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'GET /pet/findByStatus',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'GET /pet/findByStatus',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'find_pets_by_tags',
    description: 'Finds pets by tags',
    parameters: {
      "type": "object",
      "properties": {
            "tags": {
                  "type": "array",
                  "description": "Tags to filter by"
            }
      },
      "required": []
},
    handler: async (args) => {
      try {
        const config = {
          method: 'get',
          url: `${API_BASE_URL}/pet/findByTags`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
        

        // Handle query parameters and request body
        if ('GET' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'GET /pet/findByTags',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'GET /pet/findByTags',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'add_pet',
    description: 'Add a new pet to the store',
    parameters: {
      "type": "object",
      "properties": {},
      "required": []
},
    handler: async (args) => {
      try {
        const config = {
          method: 'post',
          url: `${API_BASE_URL}/pet`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
        

        // Handle query parameters and request body
        if ('POST' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'POST /pet',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'POST /pet',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'get_pet_by_id',
    description: 'Find pet by ID',
    parameters: {
      "type": "object",
      "properties": {
            "petId": {
                  "type": "integer",
                  "description": "ID of pet to return"
            }
      },
      "required": [
            "petId"
      ]
},
    handler: async (args) => {
      try {
        const config = {
          method: 'get',
          url: `${API_BASE_URL}/pet/{petId}`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
                if (args.petId) {
          finalUrl = finalUrl.replace('{petId}', encodeURIComponent(args.petId));
        }

        // Handle query parameters and request body
        if ('GET' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'GET /pet/{petId}',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'GET /pet/{petId}',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'get_inventory',
    description: 'Returns pet inventories by status',
    parameters: {
      "type": "object",
      "properties": {},
      "required": []
},
    handler: async (args) => {
      try {
        const config = {
          method: 'get',
          url: `${API_BASE_URL}/store/inventory`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
        

        // Handle query parameters and request body
        if ('GET' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'GET /store/inventory',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'GET /store/inventory',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'create_user',
    description: 'Create user',
    parameters: {
      "type": "object",
      "properties": {},
      "required": []
},
    handler: async (args) => {
      try {
        const config = {
          method: 'post',
          url: `${API_BASE_URL}/user`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
        

        // Handle query parameters and request body
        if ('POST' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'POST /user',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'POST /user',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'login_user',
    description: 'Logs user into the system',
    parameters: {
      "type": "object",
      "properties": {
            "username": {
                  "type": "string",
                  "description": "The user name for login"
            },
            "password": {
                  "type": "string",
                  "description": "The password for login in clear text"
            }
      },
      "required": []
},
    handler: async (args) => {
      try {
        const config = {
          method: 'get',
          url: `${API_BASE_URL}/user/login`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
        

        // Handle query parameters and request body
        if ('GET' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'GET /user/login',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'GET /user/login',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'logout_user',
    description: 'Logs out current logged in user session',
    parameters: {
      "type": "object",
      "properties": {},
      "required": []
},
    handler: async (args) => {
      try {
        const config = {
          method: 'get',
          url: `${API_BASE_URL}/user/logout`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
        

        // Handle query parameters and request body
        if ('GET' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'GET /user/logout',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'GET /user/logout',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'get_user_by_username',
    description: 'Get user detail based on username',
    parameters: {
      "type": "object",
      "properties": {
            "username": {
                  "type": "string",
                  "description": "The name that needs to be fetched. Use user1 for t..."
            }
      },
      "required": [
            "username"
      ]
},
    handler: async (args) => {
      try {
        const config = {
          method: 'get',
          url: `${API_BASE_URL}/user/{username}`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
                if (args.username) {
          finalUrl = finalUrl.replace('{username}', encodeURIComponent(args.username));
        }

        // Handle query parameters and request body
        if ('GET' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'GET /user/{username}',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'GET /user/{username}',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'update_existing_pet',
    description: 'Update an existing pet by Id',
    parameters: {
      "type": "object",
      "properties": {},
      "required": []
},
    handler: async (args) => {
      try {
        const config = {
          method: 'put',
          url: `${API_BASE_URL}/pet`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
        

        // Handle query parameters and request body
        if ('PUT' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'PUT /pet',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'PUT /pet',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'update_pet_with_form',
    description: 'Updates a pet resource based on the form data',
    parameters: {
      "type": "object",
      "properties": {
            "petId": {
                  "type": "integer",
                  "description": "ID of pet that needs to be updated"
            },
            "name": {
                  "type": "string",
                  "description": "Name of pet that needs to be updated"
            },
            "status": {
                  "type": "string",
                  "description": "Status of pet that needs to be updated"
            }
      },
      "required": [
            "petId"
      ]
},
    handler: async (args) => {
      try {
        const config = {
          method: 'post',
          url: `${API_BASE_URL}/pet/{petId}`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
                if (args.petId) {
          finalUrl = finalUrl.replace('{petId}', encodeURIComponent(args.petId));
        }

        // Handle query parameters and request body
        if ('POST' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'POST /pet/{petId}',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'POST /pet/{petId}',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'place_order_for_pet',
    description: 'Place a new order in the store',
    parameters: {
      "type": "object",
      "properties": {},
      "required": []
},
    handler: async (args) => {
      try {
        const config = {
          method: 'post',
          url: `${API_BASE_URL}/store/order`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
        

        // Handle query parameters and request body
        if ('POST' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'POST /store/order',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'POST /store/order',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'find_purchase_order_by_id',
    description: 'Find purchase order by ID',
    parameters: {
      "type": "object",
      "properties": {
            "orderId": {
                  "type": "integer",
                  "description": "ID of order that needs to be fetched"
            }
      },
      "required": [
            "orderId"
      ]
},
    handler: async (args) => {
      try {
        const config = {
          method: 'get',
          url: `${API_BASE_URL}/store/order/{orderId}`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
                if (args.orderId) {
          finalUrl = finalUrl.replace('{orderId}', encodeURIComponent(args.orderId));
        }

        // Handle query parameters and request body
        if ('GET' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'GET /store/order/{orderId}',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'GET /store/order/{orderId}',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'create_users_with_list_input',
    description: 'Creates list of users with given input array',
    parameters: {
      "type": "object",
      "properties": {},
      "required": []
},
    handler: async (args) => {
      try {
        const config = {
          method: 'post',
          url: `${API_BASE_URL}/user/createWithList`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
        

        // Handle query parameters and request body
        if ('POST' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'POST /user/createWithList',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'POST /user/createWithList',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'upload_image_for_pet',
    description: 'Upload image of the pet',
    parameters: {
      "type": "object",
      "properties": {
            "petId": {
                  "type": "integer",
                  "description": "ID of pet to update"
            },
            "additionalMetadata": {
                  "type": "string",
                  "description": "Additional Metadata"
            }
      },
      "required": [
            "petId"
      ]
},
    handler: async (args) => {
      try {
        const config = {
          method: 'post',
          url: `${API_BASE_URL}/pet/{petId}/uploadImage`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
                if (args.petId) {
          finalUrl = finalUrl.replace('{petId}', encodeURIComponent(args.petId));
        }

        // Handle query parameters and request body
        if ('POST' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'POST /pet/{petId}/uploadImage',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'POST /pet/{petId}/uploadImage',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
];

// Export individual tool handlers for easy access
export const find_pets_by_status = tools.find(t => t.name === 'find_pets_by_status').handler;
export const find_pets_by_tags = tools.find(t => t.name === 'find_pets_by_tags').handler;
export const add_pet = tools.find(t => t.name === 'add_pet').handler;
export const get_pet_by_id = tools.find(t => t.name === 'get_pet_by_id').handler;
export const get_inventory = tools.find(t => t.name === 'get_inventory').handler;
export const create_user = tools.find(t => t.name === 'create_user').handler;
export const login_user = tools.find(t => t.name === 'login_user').handler;
export const logout_user = tools.find(t => t.name === 'logout_user').handler;
export const get_user_by_username = tools.find(t => t.name === 'get_user_by_username').handler;
export const update_existing_pet = tools.find(t => t.name === 'update_existing_pet').handler;
export const update_pet_with_form = tools.find(t => t.name === 'update_pet_with_form').handler;
export const place_order_for_pet = tools.find(t => t.name === 'place_order_for_pet').handler;
export const find_purchase_order_by_id = tools.find(t => t.name === 'find_purchase_order_by_id').handler;
export const create_users_with_list_input = tools.find(t => t.name === 'create_users_with_list_input').handler;
export const upload_image_for_pet = tools.find(t => t.name === 'upload_image_for_pet').handler;

export default tools;
