// Auto-generated MCP tools from OpenAPI schema
// Schema: E-Commerce Management API
// Generated: 2025-06-25T18:35:48.652Z

import axios from 'axios';

const API_BASE_URL = 'http://localhost:4000/api/v2';

// Authentication configuration
// Set these environment variables or modify the values below:
const BEARERAUTH_TOKEN = process.env.BEARERAUTH_TOKEN || ''; // Bearer token
const APIKEYAUTH_KEY = process.env.APIKEYAUTH_KEY || ''; // API key


// Helper function to get authentication headers
function getAuthHeaders() {
  const headers = {
    'Content-Type': 'application/json',
    'User-Agent': 'MCP-Server/1.0'
  };

  if (BEARERAUTH_TOKEN) {
    headers['Authorization'] = `Bearer ${BEARERAUTH_TOKEN}`;
  }
  if (APIKEYAUTH_KEY) {
    headers['X-API-Key'] = APIKEYAUTH_KEY;
  }


  return headers;
}

// Tool definitions and handlers
export const tools = [
  {
    name: 'get_search',
    description: 'Global search',
    parameters: {
      "type": "object",
      "properties": {
            "q": {
                  "type": "string",
                  "description": "Search query"
            },
            "type": {
                  "type": "string",
                  "description": "Limit search to specific type"
            }
      },
      "required": [
            "q"
      ]
},
    handler: async (args) => {
      try {
        const config = {
          method: 'get',
          url: `${API_BASE_URL}/search`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
        

        // Handle query parameters and request body
        if ('GET' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'GET /search',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'GET /search',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'get_products',
    description: 'List all products',
    parameters: {
      "type": "object",
      "properties": {
            "search": {
                  "type": "string",
                  "description": "Search products by name or description"
            },
            "min_price": {
                  "type": "number",
                  "description": "Minimum price filter"
            },
            "max_price": {
                  "type": "number",
                  "description": "Maximum price filter"
            },
            "sort_by": {
                  "type": "string",
                  "description": "Sort products by field"
            },
            "sort_order": {
                  "type": "string",
                  "description": "Sort order"
            }
      },
      "required": []
},
    handler: async (args) => {
      try {
        const config = {
          method: 'get',
          url: `${API_BASE_URL}/products`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
        

        // Handle query parameters and request body
        if ('GET' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'GET /products',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'GET /products',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'get_customers',
    description: 'List customers',
    parameters: {
      "type": "object",
      "properties": {
            "search": {
                  "type": "string",
                  "description": "Search customers by name or email"
            },
            "is_premium": {
                  "type": "boolean",
                  "description": "Filter by premium status"
            }
      },
      "required": []
},
    handler: async (args) => {
      try {
        const config = {
          method: 'get',
          url: `${API_BASE_URL}/customers`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
        

        // Handle query parameters and request body
        if ('GET' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'GET /customers',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'GET /customers',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'get_orders',
    description: 'List orders',
    parameters: {
      "type": "object",
      "properties": {
            "customer_id": {
                  "type": "string",
                  "description": "Filter by customer ID"
            }
      },
      "required": []
},
    handler: async (args) => {
      try {
        const config = {
          method: 'get',
          url: `${API_BASE_URL}/orders`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
        

        // Handle query parameters and request body
        if ('GET' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'GET /orders',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'GET /orders',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'post_products',
    description: 'Create a new product',
    parameters: {
      "type": "object",
      "properties": {
            "name": {
                  "type": "string",
                  "description": "",
                  "required": true
            },
            "description": {
                  "type": "string",
                  "description": "",
                  "required": false
            },
            "price": {
                  "type": "number",
                  "description": "",
                  "required": true
            },
            "category": {
                  "type": "string",
                  "description": "",
                  "required": true
            },
            "sku": {
                  "type": "string",
                  "description": "",
                  "required": false
            },
            "stock_quantity": {
                  "type": "integer",
                  "description": "",
                  "required": false
            },
            "tags": {
                  "type": "array",
                  "description": "",
                  "required": false
            }
      },
      "required": [
            "name",
            "description",
            "price",
            "category",
            "sku",
            "stock_quantity",
            "tags"
      ]
},
    handler: async (args) => {
      try {
        const config = {
          method: 'post',
          url: `${API_BASE_URL}/products`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
        

        // Handle query parameters and request body
        if ('POST' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'POST /products',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'POST /products',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'get_products_productId',
    description: 'Get product by ID',
    parameters: {
      "type": "object",
      "properties": {
            "productId": {
                  "type": "string",
                  "description": "Unique product identifier"
            }
      },
      "required": [
            "productId"
      ]
},
    handler: async (args) => {
      try {
        const config = {
          method: 'get',
          url: `${API_BASE_URL}/products/{productId}`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
                if (args.productId) {
          finalUrl = finalUrl.replace('{productId}', encodeURIComponent(args.productId));
        }

        // Handle query parameters and request body
        if ('GET' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'GET /products/{productId}',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'GET /products/{productId}',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'post_customers',
    description: 'Create customer',
    parameters: {
      "type": "object",
      "properties": {
            "email": {
                  "type": "string",
                  "description": "",
                  "required": true
            },
            "first_name": {
                  "type": "string",
                  "description": "",
                  "required": true
            },
            "last_name": {
                  "type": "string",
                  "description": "",
                  "required": true
            },
            "phone": {
                  "type": "string",
                  "description": "",
                  "required": false
            },
            "date_of_birth": {
                  "type": "string",
                  "description": "",
                  "required": false
            },
            "address": {
                  "type": "string",
                  "description": "",
                  "required": false
            }
      },
      "required": [
            "email",
            "first_name",
            "last_name",
            "phone",
            "date_of_birth",
            "address"
      ]
},
    handler: async (args) => {
      try {
        const config = {
          method: 'post',
          url: `${API_BASE_URL}/customers`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
        

        // Handle query parameters and request body
        if ('POST' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'POST /customers',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'POST /customers',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'post_orders',
    description: 'Create order',
    parameters: {
      "type": "object",
      "properties": {
            "customer_id": {
                  "type": "string",
                  "description": "",
                  "required": true
            },
            "items": {
                  "type": "array",
                  "description": "",
                  "required": true
            },
            "payment_method": {
                  "type": "string",
                  "description": "",
                  "required": false
            }
      },
      "required": [
            "customer_id",
            "items",
            "payment_method"
      ]
},
    handler: async (args) => {
      try {
        const config = {
          method: 'post',
          url: `${API_BASE_URL}/orders`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
        

        // Handle query parameters and request body
        if ('POST' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'POST /orders',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'POST /orders',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
  {
    name: 'get_analytics_dashboard',
    description: 'Retrieve comprehensive business analytics and metrics',
    parameters: {
      "type": "object",
      "properties": {
            "period": {
                  "type": "string",
                  "description": "Analytics period"
            }
      },
      "required": []
},
    handler: async (args) => {
      try {
        const config = {
          method: 'get',
          url: `${API_BASE_URL}/analytics/dashboard`,
          headers: getAuthHeaders()
        };

        // Handle path parameters
        let finalUrl = config.url;
        

        // Handle query parameters and request body
        if ('GET' === 'GET') {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(args)) {
            if (!finalUrl.includes(`/${value}`)) {
              queryParams.append(key, value);
            }
          }
          if (queryParams.toString()) {
            config.url = finalUrl + '?' + queryParams.toString();
          } else {
            config.url = finalUrl;
          }
        } else {
          config.url = finalUrl;
          config.data = args;
        }

        const response = await axios(config);

        return {
          success: true,
          status: response.status,
          data: response.data,
          endpoint: 'GET /analytics/dashboard',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          endpoint: 'GET /analytics/dashboard',
          timestamp: new Date().toISOString()
        };
      }
    }
  },
];

// Export individual tool handlers for easy access
export const get_search = tools.find(t => t.name === 'get_search').handler;
export const get_products = tools.find(t => t.name === 'get_products').handler;
export const get_customers = tools.find(t => t.name === 'get_customers').handler;
export const get_orders = tools.find(t => t.name === 'get_orders').handler;
export const post_products = tools.find(t => t.name === 'post_products').handler;
export const get_products_productId = tools.find(t => t.name === 'get_products_productId').handler;
export const post_customers = tools.find(t => t.name === 'post_customers').handler;
export const post_orders = tools.find(t => t.name === 'post_orders').handler;
export const get_analytics_dashboard = tools.find(t => t.name === 'get_analytics_dashboard').handler;

export default tools;
