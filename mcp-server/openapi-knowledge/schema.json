{"openapi": "3.0.0", "info": {"title": "Autodesk Vault API (Mock)", "description": "AI-powered mock implementation of the Autodesk Vault API for testing and development.\nThis API simulates the Autodesk Data Management (DM) services for vault operations.\n\n## Authentication\nThis API uses Bearer token authentication obtained via OAuth three-legged flow:\n`Authorization: Bearer <token>`\n\n## Vault Operations\nAccess and manage vault items, versions, and metadata through RESTful endpoints.\n\n## AI-Powered Responses\nThis mock API uses AI to generate realistic vault data and responses based on query parameters.\n", "version": "2.0.0", "contact": {"name": "Autodesk Developer Support", "email": "<EMAIL>", "url": "https://forge.autodesk.com"}, "license": {"name": "Autodesk Developer License", "url": "https://forge.autodesk.com/en/docs/oauth/v2/developers_guide/overview/"}}, "servers": [{"url": "http://localhost:4000/AutodeskDM/Services/api/vault/v2", "description": "Local mock vault server"}, {"url": "https://test.vg.autodesk.com/AutodeskDM/Services/api/vault/v2", "description": "Test vault gateway"}], "security": [{"bearerAuth": []}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "Bearer token obtained via three-legged OAuth flow.\nMust be included in Authorization header: `Bearer <token>`\n"}}, "schemas": {"ItemVersion": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the item version", "example": "Assembly1"}, "id": {"type": "string", "description": "Unique identifier of the item version", "example": "57"}, "revision": {"type": "string", "description": "Revision identifier", "example": "A"}, "lastModifiedUserName": {"type": "string", "description": "Username of the last modifier", "example": "TestAccount"}, "lastModifiedDate": {"type": "string", "format": "date-time", "description": "Last modification timestamp", "example": "2024-10-22T08:21:31.65Z"}, "number": {"type": "string", "description": "Item number", "example": "Assembly1"}, "title": {"type": "string", "description": "Item title", "example": "Assembly1"}, "description": {"type": "string", "description": "Item description", "example": ""}, "comment": {"type": "string", "description": "Item comment", "example": ""}, "state": {"type": "string", "description": "Current state of the item", "example": "Work in Progress"}, "stateColor": {"type": "integer", "description": "Color code for the state", "example": -********}, "category": {"type": "string", "description": "Item category", "example": "Assembly"}, "categoryColor": {"type": "integer", "description": "Color code for the category", "example": -********}, "isReadOnly": {"type": "boolean", "description": "Whether the item is read-only", "example": false}, "isCloaked": {"type": "boolean", "description": "Whether the item is cloaked", "example": false}, "item": {"$ref": "#/components/schemas/ItemReference"}, "version": {"type": "integer", "description": "Version number", "example": 2}, "entityType": {"type": "string", "description": "Type of entity", "example": "ItemVersion"}, "url": {"type": "string", "description": "API URL for this item version", "example": "/AutodeskDM/Services/api/vault/v2/vaults/117/item-versions/57"}}}, "ItemReference": {"type": "object", "properties": {"id": {"type": "string", "description": "Item ID", "example": "55"}, "entityType": {"type": "string", "description": "Entity type", "example": "<PERSON><PERSON>"}, "url": {"type": "string", "description": "API URL for the item", "example": "/AutodeskDM/Services/api/vault/v2/vaults/117/items/55"}}}, "Pagination": {"type": "object", "properties": {"limit": {"type": "integer", "description": "Maximum number of results per page", "example": 1000}, "totalResults": {"type": "integer", "description": "Total number of results", "example": 2}, "indexingStatus": {"type": "string", "description": "Status of indexing", "enum": ["IndexingComplete", "IndexingInProgress", "IndexingFailed"], "example": "IndexingComplete"}, "cursorState": {"type": "string", "description": "Cursor state for pagination", "example": "eyJpZCI6IjU3In0="}}}, "ItemVersionsResponse": {"type": "object", "properties": {"pagination": {"$ref": "#/components/schemas/Pagination"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/ItemVersion"}}, "included": {"type": "object", "description": "Additional included data", "additionalProperties": true}}}, "Error": {"type": "object", "properties": {"error": {"type": "string", "description": "Error code", "example": "unauthorized"}, "message": {"type": "string", "description": "Human-readable error message", "example": "The supplied authorization header was not valid"}, "details": {"type": "object", "description": "Additional error details", "additionalProperties": true}, "timestamp": {"type": "string", "format": "date-time", "description": "Error timestamp", "example": "2024-10-22T08:21:31.65Z"}}}}, "parameters": {"VaultIdParam": {"name": "vaultId", "in": "path", "required": true, "description": "The unique identifier of a vault", "schema": {"type": "string", "example": "117"}}, "SearchQueryParam": {"name": "q", "in": "query", "description": "The value to use for the search. Based on 'SearchContent' option, this parameter will\neither search across all properties or across all properties and content.\nEx: q=Assembly, all objects that contain 'Assembly' within their properties will be returned.\n", "required": false, "schema": {"type": "string", "example": "Assembly"}}, "PropDefIdsParam": {"name": "option[propDefIds]", "in": "query", "description": "The properties that need to be returned. Property ids separated by ',', e.g. '1,2,3'\n'all' means return all properties.\n", "required": false, "schema": {"type": "string", "example": "all"}}, "ReleasedItemsOnlyParam": {"name": "option[releasedItemsOnly]", "in": "query", "description": "true: Include only versions that are consumable (released) state.\nfalse: (Default) Include all versions.\n", "required": false, "schema": {"type": "boolean", "default": false}}, "LatestOnlyParam": {"name": "option[latestOnly]", "in": "query", "description": "true: (Default) Include only the latest version.\nfalse: Include all versions.\n", "required": false, "schema": {"type": "boolean", "default": true}}, "SortParam": {"name": "sort", "in": "query", "description": "Specifies sorting criteria for search results. Format: {propertyDefSysName} {sort-order}\nAccepted values for sort-order: asc, desc. Ex: sort = Revision desc,Name asc\n", "required": false, "schema": {"type": "string", "example": "Revision desc,Name asc"}}, "LimitParam": {"name": "limit", "in": "query", "description": "Specifies the number of results to return per page. Maximum limit is controlled by\n\"Page size configuration\" setting which could be updated using ADMS.\n", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 1000, "default": 1000, "example": 100}}, "CursorStateParam": {"name": "cursorState", "in": "query", "description": "Indicates the state of the cursor for pagination. Use this parameter to navigate through paged results", "required": false, "schema": {"type": "string", "example": "eyJpZCI6IjU3In0="}}}, "responses": {"BadRequest": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}, "example": {"error": "bad_request", "message": "The server was unable to process the request. The syntax of the request is malformed or the request is missing a required header.", "timestamp": "2024-10-22T08:21:31.65Z"}}}}, "Unauthorized": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}, "example": {"error": "unauthorized", "message": "The supplied authorization header was not valid or the supplied token scope was not acceptable.", "timestamp": "2024-10-22T08:21:31.65Z"}}}}, "Forbidden": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}, "example": {"error": "forbidden", "message": "The request was successfully validated but lacking the required permissions.", "timestamp": "2024-10-22T08:21:31.65Z"}}}}}}, "paths": {"/vaults/{vaultId}/item-versions": {"get": {"summary": "Get item versions in vault", "description": "Get item versions in the given Vault with vaultId. This endpoint allows you to search\nand retrieve item versions with various filtering and sorting options.\n\n## Authentication Context\nUser context required - must provide valid Bearer token from OAuth three-legged flow.\n\n## Search Capabilities\n- Search across all properties or properties and content\n- Filter by release state and version constraints\n- Sort by multiple criteria\n- Paginated results with cursor-based navigation\n", "tags": ["Vault Items"], "security": [{"bearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/VaultIdParam"}, {"$ref": "#/components/parameters/SearchQueryParam"}, {"$ref": "#/components/parameters/PropDefIdsParam"}, {"$ref": "#/components/parameters/ReleasedItemsOnlyParam"}, {"$ref": "#/components/parameters/LatestOnlyParam"}, {"$ref": "#/components/parameters/SortParam"}, {"$ref": "#/components/parameters/LimitParam"}, {"$ref": "#/components/parameters/CursorStateParam"}], "responses": {"200": {"description": "The request succeeded. The response contains the list of item versions.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemVersionsResponse"}, "example": {"pagination": {"limit": 1000, "totalResults": 2, "indexingStatus": "IndexingComplete"}, "results": [{"name": "Assembly1", "id": "57", "revision": "A", "lastModifiedUserName": "TestAccount", "lastModifiedDate": "2024-10-22T08:21:31.65Z", "number": "Assembly1", "title": "Assembly1", "description": "", "comment": "", "state": "Work in Progress", "stateColor": -********, "category": "Assembly", "categoryColor": -********, "isReadOnly": false, "isCloaked": false, "item": {"id": "55", "entityType": "<PERSON><PERSON>", "url": "/AutodeskDM/Services/api/vault/v2/vaults/117/items/55"}, "version": 2, "entityType": "ItemVersion", "url": "/AutodeskDM/Services/api/vault/v2/vaults/117/item-versions/57"}, {"name": "100001", "id": "53", "revision": "A", "lastModifiedUserName": "TestAccount", "lastModifiedDate": "2024-10-22T08:19:49.753Z", "number": "100001", "title": "", "description": "", "comment": "", "state": "Work in Progress", "stateColor": -********, "category": "Document", "categoryColor": -3251596, "isReadOnly": false, "isCloaked": false, "item": {"id": "52", "entityType": "<PERSON><PERSON>", "url": "/AutodeskDM/Services/api/vault/v2/vaults/117/items/52"}, "version": 1, "entityType": "ItemVersion", "url": "/AutodeskDM/Services/api/vault/v2/vaults/117/item-versions/53"}], "included": {}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}}, "tags": [{"name": "Vault Items", "description": "Vault item version management and retrieval"}]}