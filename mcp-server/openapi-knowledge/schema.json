{"openapi": "3.0.0", "info": {"title": "E-Commerce Management API", "description": "A comprehensive e-commerce management system API that handles products, orders, customers, \ninventory, analytics, and more. This API demonstrates complex real-world scenarios with \nauthentication, pagination, filtering, and various business operations.\n\n## Authentication\nThis API uses Bearer token authentication. Include the token in the Authorization header:\n`Authorization: Bearer <your-token>`\n\n## Rate Limiting\nAPI calls are limited to 1000 requests per hour per API key.\n\n## Pagination\nList endpoints support pagination using `page` and `limit` parameters.\n", "version": "2.1.0", "contact": {"name": "API Support", "email": "<EMAIL>", "url": "https://docs.ecommerce-api.com"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}}, "servers": [{"url": "http://localhost:4000/api/v2", "description": "Local development server"}, {"url": "https://api.ecommerce-demo.com/v2", "description": "Production server"}], "security": [{"bearerAuth": []}, {"apiKeyAuth": []}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT Bearer token authentication. Must be obtained via OAuth flow.\nFormat: `Bearer <token>`\n"}, "apiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "X-API-Key", "description": "API key for service-to-service authentication"}}, "schemas": {"Product": {"type": "object", "required": ["name", "price", "category"], "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "name": {"type": "string", "minLength": 1, "maxLength": 200, "example": "Premium Wireless Headphones"}, "description": {"type": "string", "maxLength": 2000, "example": "High-quality wireless headphones with noise cancellation"}, "price": {"type": "number", "format": "float", "minimum": 0, "example": 299.99}, "category": {"type": "string", "enum": ["electronics", "clothing", "books", "home", "sports", "beauty"], "example": "electronics"}, "sku": {"type": "string", "pattern": "^[A-Z0-9-]+$", "example": "WH-1000XM4"}, "stock_quantity": {"type": "integer", "minimum": 0, "example": 150}, "is_active": {"type": "boolean", "default": true}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["wireless", "bluetooth", "noise-cancelling"]}, "created_at": {"type": "string", "format": "date-time", "readOnly": true}, "updated_at": {"type": "string", "format": "date-time", "readOnly": true}}}, "Customer": {"type": "object", "required": ["email", "first_name", "last_name"], "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "first_name": {"type": "string", "minLength": 1, "maxLength": 50, "example": "<PERSON>"}, "last_name": {"type": "string", "minLength": 1, "maxLength": 50, "example": "<PERSON><PERSON>"}, "phone": {"type": "string", "pattern": "^\\+?[1-9]\\d{1,14}$", "example": "+1234567890"}, "date_of_birth": {"type": "string", "format": "date", "example": "1990-05-15"}, "address": {"$ref": "#/components/schemas/Address"}, "loyalty_points": {"type": "integer", "minimum": 0, "default": 0, "example": 1250}, "is_premium": {"type": "boolean", "default": false}, "created_at": {"type": "string", "format": "date-time", "readOnly": true}}}, "Address": {"type": "object", "required": ["street", "city", "country", "postal_code"], "properties": {"street": {"type": "string", "example": "123 Main Street"}, "city": {"type": "string", "example": "New York"}, "state": {"type": "string", "example": "NY"}, "country": {"type": "string", "minLength": 2, "maxLength": 2, "example": "US"}, "postal_code": {"type": "string", "example": "10001"}}}, "Order": {"type": "object", "required": ["customer_id", "items"], "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "customer_id": {"type": "string", "format": "uuid"}, "items": {"type": "array", "minItems": 1, "items": {"$ref": "#/components/schemas/OrderItem"}}, "status": {"type": "string", "enum": ["pending", "confirmed", "processing", "shipped", "delivered", "cancelled"], "default": "pending"}, "total_amount": {"type": "number", "format": "float", "readOnly": true}, "shipping_address": {"$ref": "#/components/schemas/Address"}, "payment_method": {"type": "string", "enum": ["credit_card", "debit_card", "paypal", "bank_transfer"]}, "notes": {"type": "string", "maxLength": 500}, "created_at": {"type": "string", "format": "date-time", "readOnly": true}, "estimated_delivery": {"type": "string", "format": "date"}}}, "OrderItem": {"type": "object", "required": ["product_id", "quantity"], "properties": {"product_id": {"type": "string", "format": "uuid"}, "quantity": {"type": "integer", "minimum": 1, "example": 2}, "unit_price": {"type": "number", "format": "float", "readOnly": true}}}, "Analytics": {"type": "object", "properties": {"total_revenue": {"type": "number", "format": "float"}, "total_orders": {"type": "integer"}, "total_customers": {"type": "integer"}, "top_products": {"type": "array", "items": {"type": "object", "properties": {"product_id": {"type": "string"}, "name": {"type": "string"}, "sales_count": {"type": "integer"}}}}, "revenue_by_category": {"type": "object", "additionalProperties": {"type": "number"}}}}, "Error": {"type": "object", "required": ["error", "message"], "properties": {"error": {"type": "string", "example": "validation_error"}, "message": {"type": "string", "example": "The provided data is invalid"}, "details": {"type": "object"}, "timestamp": {"type": "string", "format": "date-time"}}}}, "parameters": {"PageParam": {"name": "page", "in": "query", "description": "Page number for pagination (starts from 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, "LimitParam": {"name": "limit", "in": "query", "description": "Number of items per page (max 100)", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20, "example": 20}}, "CategoryParam": {"name": "category", "in": "query", "description": "Filter by product category", "required": false, "schema": {"type": "string", "enum": ["electronics", "clothing", "books", "home", "sports", "beauty"]}}, "StatusParam": {"name": "status", "in": "query", "description": "Filter by order status", "required": false, "schema": {"type": "string", "enum": ["pending", "confirmed", "processing", "shipped", "delivered", "cancelled"]}}}, "responses": {"NotFound": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}, "example": {"error": "not_found", "message": "The requested resource was not found", "timestamp": "2023-12-01T10:30:00Z"}}}}, "Unauthorized": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}, "example": {"error": "unauthorized", "message": "Valid authentication token required", "timestamp": "2023-12-01T10:30:00Z"}}}}, "ValidationError": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}, "example": {"error": "validation_error", "message": "Invalid input data", "details": {"field": "email", "issue": "Invalid email format"}, "timestamp": "2023-12-01T10:30:00Z"}}}}}}, "paths": {"/products": {"get": {"summary": "List all products", "description": "Retrieve a paginated list of products with optional filtering by category, \nprice range, and search terms. Supports sorting by various fields.\n", "tags": ["Products"], "parameters": [{"$ref": "#/components/parameters/PageParam"}, {"$ref": "#/components/parameters/LimitParam"}, {"$ref": "#/components/parameters/CategoryParam"}, {"name": "search", "in": "query", "description": "Search products by name or description", "schema": {"type": "string", "example": "wireless headphones"}}, {"name": "min_price", "in": "query", "description": "Minimum price filter", "schema": {"type": "number", "format": "float", "minimum": 0}}, {"name": "max_price", "in": "query", "description": "Maximum price filter", "schema": {"type": "number", "format": "float", "minimum": 0}}, {"name": "sort_by", "in": "query", "description": "Sort products by field", "schema": {"type": "string", "enum": ["name", "price", "created_at", "stock_quantity"], "default": "created_at"}}, {"name": "sort_order", "in": "query", "description": "Sort order", "schema": {"type": "string", "enum": ["asc", "desc"], "default": "desc"}}], "responses": {"200": {"description": "List of products", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer"}, "limit": {"type": "integer"}, "total": {"type": "integer"}, "pages": {"type": "integer"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}, "post": {"summary": "Create a new product", "description": "Create a new product in the catalog. Requires admin privileges.\nThe product will be automatically assigned a unique ID and timestamps.\n", "tags": ["Products"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name", "price", "category"], "properties": {"name": {"type": "string", "minLength": 1, "maxLength": 200}, "description": {"type": "string", "maxLength": 2000}, "price": {"type": "number", "format": "float", "minimum": 0}, "category": {"type": "string", "enum": ["electronics", "clothing", "books", "home", "sports", "beauty"]}, "sku": {"type": "string", "pattern": "^[A-Z0-9-]+$"}, "stock_quantity": {"type": "integer", "minimum": 0, "default": 0}, "tags": {"type": "array", "items": {"type": "string"}}}}, "example": {"name": "Smart Watch Pro", "description": "Advanced fitness tracking smartwatch", "price": 399.99, "category": "electronics", "sku": "SW-PRO-001", "stock_quantity": 100, "tags": ["smartwatch", "fitness", "bluetooth"]}}}}, "responses": {"201": {"description": "Product created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/products/{productId}": {"get": {"summary": "Get product by ID", "description": "Retrieve detailed information about a specific product", "tags": ["Products"], "parameters": [{"name": "productId", "in": "path", "required": true, "description": "Unique product identifier", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Product details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}}}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/customers": {"get": {"summary": "List customers", "description": "Retrieve a paginated list of customers with optional filtering", "tags": ["Customers"], "security": [{"bearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/PageParam"}, {"$ref": "#/components/parameters/LimitParam"}, {"name": "search", "in": "query", "description": "Search customers by name or email", "schema": {"type": "string"}}, {"name": "is_premium", "in": "query", "description": "Filter by premium status", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "List of customers", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Customer"}}, "pagination": {"type": "object"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}, "post": {"summary": "Create customer", "description": "Register a new customer account", "tags": ["Customers"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "first_name", "last_name"], "properties": {"email": {"type": "string", "format": "email"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "phone": {"type": "string"}, "date_of_birth": {"type": "string", "format": "date"}, "address": {"$ref": "#/components/schemas/Address"}}}}}}, "responses": {"201": {"description": "Customer created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Customer"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}}}}, "/orders": {"get": {"summary": "List orders", "description": "Retrieve orders with filtering and pagination", "tags": ["Orders"], "security": [{"bearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/PageParam"}, {"$ref": "#/components/parameters/LimitParam"}, {"$ref": "#/components/parameters/StatusParam"}, {"name": "customer_id", "in": "query", "description": "Filter by customer ID", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "List of orders", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Order"}}, "pagination": {"type": "object"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}, "post": {"summary": "Create order", "description": "Place a new order for a customer", "tags": ["Orders"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["customer_id", "items"], "properties": {"customer_id": {"type": "string", "format": "uuid"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/OrderItem"}}, "payment_method": {"type": "string", "enum": ["credit_card", "debit_card", "paypal", "bank_transfer"]}}}}}}, "responses": {"201": {"description": "Order created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Order"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/analytics/dashboard": {"get": {"summary": "Get analytics dashboard", "description": "Retrieve comprehensive business analytics and metrics", "tags": ["Analytics"], "security": [{"bearerAuth": []}], "parameters": [{"name": "period", "in": "query", "description": "Analytics period", "schema": {"type": "string", "enum": ["day", "week", "month", "quarter", "year"], "default": "month"}}], "responses": {"200": {"description": "Analytics data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Analytics"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/search": {"get": {"summary": "Global search", "description": "Search across products, customers, and orders", "tags": ["Search"], "parameters": [{"name": "q", "in": "query", "required": true, "description": "Search query", "schema": {"type": "string", "minLength": 2}}, {"name": "type", "in": "query", "description": "Limit search to specific type", "schema": {"type": "string", "enum": ["products", "customers", "orders"]}}], "responses": {"200": {"description": "Search results", "content": {"application/json": {"schema": {"type": "object", "properties": {"query": {"type": "string"}, "results": {"type": "object"}, "total_results": {"type": "integer"}}}}}}}}}}, "tags": [{"name": "Products", "description": "Product catalog management"}, {"name": "Customers", "description": "Customer management"}, {"name": "Orders", "description": "Order processing and management"}, {"name": "Analytics", "description": "Business analytics and reporting"}, {"name": "Search", "description": "Search functionality"}]}