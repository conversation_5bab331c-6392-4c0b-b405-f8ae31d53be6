import MCPFunctionClient from './client.js';

async function runTests() {
  console.log('🚀 Starting MCP Function Calling Tests\n');
  
  const client = new MCPFunctionClient();
  
  try {
    console.log('1. Connecting to MCP server...');
    const connected = await client.connect();
    
    if (!connected) {
      console.error('❌ Failed to connect to server');
      return;
    }
    
    console.log('✅ Connected successfully\n');

    console.log('2. Testing direct function calls...\n');
    
    console.log('--- Testing get_weather ---');
    await client.callFunction('get_weather', {
      location: 'San Francisco, CA',
      unit: 'celsius'
    });
    
    console.log('\n--- Testing calculate ---');
    await client.callFunction('calculate', {
      expression: '2 + 2 * 3'
    });
    
    console.log('\n--- Testing generate_uuid ---');
    await client.callFunction('generate_uuid', {
      version: 4
    });
    
    console.log('\n--- Testing get_system_info ---');
    await client.callFunction('get_system_info', {
      info_type: 'time'
    });

    console.log('\n3. Testing OpenAI-style function calling simulation...\n');
    
    const testQueries = [
      {
        role: 'user',
        content: 'What is the weather in New York?'
      },
      {
        role: 'user', 
        content: 'Calculate 15 * 7 + 3'
      },
      {
        role: 'user',
        content: 'Generate a UUID for me'
      },
      {
        role: 'user',
        content: 'What time is it?'
      }
    ];

    for (const query of testQueries) {
      console.log(`\n--- Processing: "${query.content}" ---`);
      const response = await client.simulateOpenAIFunctionCalling([query]);
      console.log('Response:', response.content);
    }

    console.log('\n4. Testing available tools listing...');
    const tools = client.getAvailableTools();
    console.log('Available tools:');
    tools.forEach(tool => {
      console.log(`- ${tool.name}: ${tool.description}`);
    });

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await client.disconnect();
    console.log('\n✅ Tests completed');
    process.exit(0);
  }
}

runTests().catch(console.error);
