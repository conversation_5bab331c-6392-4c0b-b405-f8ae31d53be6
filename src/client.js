import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { spawn } from 'child_process';

class MCPFunctionClient {
  constructor() {
    this.client = new Client(
      {
        name: 'mcp-function-client',
        version: '1.0.0',
      },
      {
        capabilities: {},
      }
    );
    
    this.connected = false;
    this.availableTools = [];
  }

  async connect(serverCommand = null) {
    try {
      let serverProcess;

      if (serverCommand) {
        serverProcess = spawn(serverCommand[0], serverCommand.slice(1), {
          stdio: ['pipe', 'pipe', 'inherit'],
          cwd: process.cwd()
        });
      } else {
        // Default to running the server script
        serverProcess = spawn('node', ['src/server.js'], {
          stdio: ['pipe', 'pipe', 'inherit'],
          cwd: process.cwd()
        });
      }

      // Wait a moment for the server to start
      await new Promise(resolve => setTimeout(resolve, 1000));

      const transport = new StdioClientTransport({
        stdin: serverProcess.stdin,
        stdout: serverProcess.stdout
      });

      await this.client.connect(transport);
      this.connected = true;

      await this.loadAvailableTools();
      console.log('Connected to MCP server');
      console.log(`Available tools: ${this.availableTools.map(t => t.name).join(', ')}`);

      return true;
    } catch (error) {
      console.error('Failed to connect to MCP server:', error.message);
      return false;
    }
  }

  async loadAvailableTools() {
    if (!this.connected) {
      throw new Error('Not connected to server');
    }

    try {
      const response = await this.client.request(
        { method: 'tools/list' },
        { method: 'tools/list' }
      );
      
      this.availableTools = response.tools || [];
      return this.availableTools;
    } catch (error) {
      console.error('Failed to load tools:', error.message);
      return [];
    }
  }

  async callFunction(functionName, parameters = {}) {
    if (!this.connected) {
      throw new Error('Not connected to server');
    }

    const tool = this.availableTools.find(t => t.name === functionName);
    if (!tool) {
      throw new Error(`Function ${functionName} not found. Available: ${this.availableTools.map(t => t.name).join(', ')}`);
    }

    try {
      console.log(`Calling function: ${functionName}`);
      console.log(`Parameters:`, parameters);

      const response = await this.client.request(
        {
          method: 'tools/call',
          params: {
            name: functionName,
            arguments: parameters
          }
        },
        { method: 'tools/call' }
      );

      if (response.isError) {
        throw new Error(`Function call failed: ${response.content[0]?.text || 'Unknown error'}`);
      }

      const result = JSON.parse(response.content[0]?.text || '{}');
      console.log(`Result:`, result);
      return result;
    } catch (error) {
      console.error(`Error calling function ${functionName}:`, error.message);
      throw error;
    }
  }

  getAvailableTools() {
    return this.availableTools.map(tool => ({
      name: tool.name,
      description: tool.description,
      parameters: tool.inputSchema
    }));
  }

  async simulateOpenAIFunctionCalling(messages, availableFunctions = null) {
    const functions = availableFunctions || this.getAvailableTools();
    
    console.log('\n=== OpenAI-style Function Calling Simulation ===');
    console.log('Available functions:', functions.map(f => f.name));
    
    const lastMessage = messages[messages.length - 1];
    if (!lastMessage || lastMessage.role !== 'user') {
      throw new Error('Last message must be from user');
    }

    const userQuery = lastMessage.content;
    console.log(`User query: ${userQuery}`);

    const functionToCall = this.selectFunctionForQuery(userQuery, functions);
    
    if (!functionToCall) {
      return {
        role: 'assistant',
        content: 'I cannot determine which function to call for this query.'
      };
    }

    console.log(`Selected function: ${functionToCall.name}`);

    const parameters = this.extractParametersFromQuery(userQuery, functionToCall);
    console.log(`Extracted parameters:`, parameters);

    try {
      const result = await this.callFunction(functionToCall.name, parameters);
      
      return {
        role: 'assistant',
        content: `I called the ${functionToCall.name} function and got the following result: ${JSON.stringify(result, null, 2)}`,
        function_call: {
          name: functionToCall.name,
          arguments: JSON.stringify(parameters)
        },
        function_result: result
      };
    } catch (error) {
      return {
        role: 'assistant',
        content: `Error calling function ${functionToCall.name}: ${error.message}`,
        function_call: {
          name: functionToCall.name,
          arguments: JSON.stringify(parameters)
        },
        error: error.message
      };
    }
  }

  selectFunctionForQuery(query, functions) {
    const queryLower = query.toLowerCase();
    
    for (const func of functions) {
      const funcName = func.name.toLowerCase();
      const funcDesc = func.description.toLowerCase();
      
      if (queryLower.includes('weather') && funcName.includes('weather')) {
        return func;
      }
      if ((queryLower.includes('calculate') || queryLower.includes('math')) && funcName.includes('calculate')) {
        return func;
      }
      if (queryLower.includes('uuid') && funcName.includes('uuid')) {
        return func;
      }
      if ((queryLower.includes('system') || queryLower.includes('time') || queryLower.includes('info')) && funcName.includes('system')) {
        return func;
      }
    }
    
    return null;
  }

  extractParametersFromQuery(query, functionDef) {
    const parameters = {};
    const queryLower = query.toLowerCase();
    
    if (functionDef.name === 'get_weather') {
      const locationMatch = query.match(/(?:weather (?:in|for) |in )([^,\n]+)/i);
      if (locationMatch) {
        parameters.location = locationMatch[1].trim();
      }
      
      if (queryLower.includes('celsius') || queryLower.includes('°c')) {
        parameters.unit = 'celsius';
      } else if (queryLower.includes('fahrenheit') || queryLower.includes('°f')) {
        parameters.unit = 'fahrenheit';
      }
    }
    
    if (functionDef.name === 'calculate') {
      const mathMatch = query.match(/calculate\s+(.+)|what\s+is\s+(.+)|solve\s+(.+)/i);
      if (mathMatch) {
        parameters.expression = (mathMatch[1] || mathMatch[2] || mathMatch[3]).trim();
      }
    }
    
    if (functionDef.name === 'get_system_info') {
      if (queryLower.includes('time')) {
        parameters.info_type = 'time';
      } else if (queryLower.includes('platform')) {
        parameters.info_type = 'platform';
      } else if (queryLower.includes('memory')) {
        parameters.info_type = 'memory';
      } else {
        parameters.info_type = 'all';
      }
    }
    
    return parameters;
  }

  async disconnect() {
    if (this.connected) {
      await this.client.close();
      this.connected = false;
      console.log('Disconnected from MCP server');
    }
  }
}

export default MCPFunctionClient;
