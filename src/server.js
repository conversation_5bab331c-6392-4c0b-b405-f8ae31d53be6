import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js';
import { v4 as uuidv4 } from 'uuid';

class MCPFunctionServer {
  constructor() {
    this.server = new Server(
      {
        name: 'mcp-function-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.tools = new Map();
    this.setupHandlers();
    this.registerDefaultTools();
  }

  setupHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: Array.from(this.tools.values()).map(tool => ({
          name: tool.name,
          description: tool.description,
          inputSchema: tool.inputSchema
        }))
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;
      
      if (!this.tools.has(name)) {
        throw new Error(`Tool ${name} not found`);
      }

      const tool = this.tools.get(name);
      
      try {
        const result = await tool.handler(args || {});
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(result, null, 2)
            }
          ]
        };
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error executing tool ${name}: ${error.message}`
            }
          ],
          isError: true
        };
      }
    });
  }

  registerTool(toolDefinition) {
    const { name, description, parameters, handler } = toolDefinition;
    
    const inputSchema = {
      type: 'object',
      properties: parameters?.properties || {},
      required: parameters?.required || []
    };

    this.tools.set(name, {
      name,
      description,
      inputSchema,
      handler
    });

    console.log(`Registered tool: ${name}`);
  }

  registerDefaultTools() {
    this.registerTool({
      name: 'get_weather',
      description: 'Get current weather information for a location',
      parameters: {
        properties: {
          location: {
            type: 'string',
            description: 'The city and state, e.g. San Francisco, CA'
          },
          unit: {
            type: 'string',
            enum: ['celsius', 'fahrenheit'],
            description: 'Temperature unit'
          }
        },
        required: ['location']
      },
      handler: async (args) => {
        const { location, unit = 'fahrenheit' } = args;
        
        const mockWeatherData = {
          location,
          temperature: unit === 'celsius' ? 22 : 72,
          unit,
          condition: 'sunny',
          humidity: 65,
          wind_speed: 10,
          timestamp: new Date().toISOString()
        };

        return {
          success: true,
          data: mockWeatherData
        };
      }
    });

    this.registerTool({
      name: 'calculate',
      description: 'Perform mathematical calculations',
      parameters: {
        properties: {
          expression: {
            type: 'string',
            description: 'Mathematical expression to evaluate (e.g., "2 + 2", "sqrt(16)")'
          }
        },
        required: ['expression']
      },
      handler: async (args) => {
        const { expression } = args;
        
        try {
          const sanitizedExpression = expression.replace(/[^0-9+\-*/().\s]/g, '');
          const result = eval(sanitizedExpression);
          
          return {
            success: true,
            expression,
            result,
            timestamp: new Date().toISOString()
          };
        } catch (error) {
          throw new Error(`Invalid mathematical expression: ${expression}`);
        }
      }
    });

    this.registerTool({
      name: 'generate_uuid',
      description: 'Generate a new UUID',
      parameters: {
        properties: {
          version: {
            type: 'number',
            enum: [1, 4],
            description: 'UUID version (1 or 4)',
            default: 4
          }
        }
      },
      handler: async (args) => {
        const { version = 4 } = args;
        
        let uuid;
        if (version === 4) {
          uuid = uuidv4();
        } else {
          uuid = uuidv4();
        }

        return {
          success: true,
          uuid,
          version,
          timestamp: new Date().toISOString()
        };
      }
    });

    this.registerTool({
      name: 'get_system_info',
      description: 'Get system information',
      parameters: {
        properties: {
          info_type: {
            type: 'string',
            enum: ['time', 'platform', 'memory', 'all'],
            description: 'Type of system information to retrieve'
          }
        },
        required: ['info_type']
      },
      handler: async (args) => {
        const { info_type } = args;
        
        const systemInfo = {
          time: new Date().toISOString(),
          platform: process.platform,
          node_version: process.version,
          memory: {
            used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
            total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
          }
        };

        let result;
        switch (info_type) {
          case 'time':
            result = { time: systemInfo.time };
            break;
          case 'platform':
            result = { 
              platform: systemInfo.platform,
              node_version: systemInfo.node_version 
            };
            break;
          case 'memory':
            result = { memory: systemInfo.memory };
            break;
          case 'all':
            result = systemInfo;
            break;
          default:
            throw new Error(`Unknown info_type: ${info_type}`);
        }

        return {
          success: true,
          data: result,
          timestamp: new Date().toISOString()
        };
      }
    });
  }

  async start() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.log('MCP Function Server started on stdio');
  }
}

const server = new MCPFunctionServer();
server.start().catch(console.error);

export default MCPFunctionServer;
