import express from 'express';
import cors from 'cors';
import axios from 'axios';

const app = express();
const PORT = 8000;

// MCP Server configuration (can be on different machine)
const MCP_SERVER_URL = process.env.MCP_SERVER_URL || 'http://localhost:3000';

app.use(cors());
app.use(express.json());

// Get available tools from MCP server
async function getTools() {
  try {
    const response = await axios.get(`${MCP_SERVER_URL}/tools`);
    return response.data.tools;
  } catch (error) {
    console.error('Failed to get tools:', error.message);
    return [];
  }
}

// Call a tool on the MCP server
async function callTool(name, args) {
  try {
    const response = await axios.post(`${MCP_SERVER_URL}/tools/call`, {
      name,
      arguments: args
    });
    return response.data;
  } catch (error) {
    throw new Error(`Tool call failed: ${error.response?.data?.error || error.message}`);
  }
}

// Use AI to select the best tool for the user's request
async function selectFunction(message, tools) {
  // Find an AI tool to help with selection
  const aiTool = tools.find(tool =>
    tool.description.toLowerCase().includes('ai') ||
    tool.description.toLowerCase().includes('question') ||
    tool.name.toLowerCase().includes('ai')
  );

  if (!aiTool) {
    // No AI tool available, use simple scoring fallback
    return tools[0];
  }

  const toolsDescription = tools.map(tool =>
    `${tool.name}: ${tool.description}`
  ).join('\n');

  const prompt = `Given this user request: "${message}"

Available tools:
${toolsDescription}

Which tool should be used? Respond with only the tool name.`;

  try {
    // Use the first parameter that seems like a question/text parameter
    const aiParams = aiTool.parameters?.properties || {};
    const questionParam = Object.keys(aiParams).find(param =>
      param.toLowerCase().includes('question') ||
      param.toLowerCase().includes('text') ||
      param.toLowerCase().includes('query')
    ) || Object.keys(aiParams)[0];

    const response = await axios.post(`${MCP_SERVER_URL}/tools/call`, {
      name: aiTool.name,
      arguments: { [questionParam]: prompt }
    });

    const aiResponse = response.data.result.answer || response.data.result[Object.keys(response.data.result)[0]];
    const selectedToolName = String(aiResponse).trim().toLowerCase();

    // Find the tool by name
    const selectedTool = tools.find(tool =>
      tool.name.toLowerCase() === selectedToolName ||
      tool.name.toLowerCase().includes(selectedToolName) ||
      selectedToolName.includes(tool.name.toLowerCase())
    );

    return selectedTool || tools[0];
  } catch (error) {
    console.error('AI tool selection failed:', error.message);
    return tools[0]; // fallback to first tool
  }
}

// Use AI to extract parameters for the selected tool
async function extractParameters(message, tool) {
  const properties = tool.parameters?.properties || {};
  const required = tool.parameters?.required || [];

  if (Object.keys(properties).length === 0) {
    return {}; // No parameters needed
  }

  const paramDescriptions = Object.entries(properties).map(([name, schema]) =>
    `${name} (${schema.type || 'string'}): ${schema.description || 'No description'}`
  ).join('\n');

  const prompt = `Extract parameters from this user request: "${message}"

Tool: ${tool.name}
Required parameters: ${required.join(', ') || 'none'}

Parameter definitions:
${paramDescriptions}

Return a JSON object with the parameter values. If a parameter cannot be determined from the message, omit it unless it's required.`;

  try {
    const response = await axios.post(`${MCP_SERVER_URL}/tools/call`, {
      name: 'ask_ai',
      arguments: { question: prompt }
    });

    const aiResponse = response.data.result.answer;

    // Try to parse JSON from AI response
    const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      try {
        return JSON.parse(jsonMatch[0]);
      } catch (parseError) {
        console.error('Failed to parse AI parameter extraction:', parseError.message);
      }
    }

    // Fallback: return empty object or required params with message
    const fallbackParams = {};
    for (const param of required) {
      fallbackParams[param] = message;
    }
    return fallbackParams;

  } catch (error) {
    console.error('AI parameter extraction failed:', error.message);
    // Fallback: return required parameters with the full message
    const fallbackParams = {};
    for (const param of required) {
      fallbackParams[param] = message;
    }
    return fallbackParams;
  }
}

// OpenAI-compatible chat completions endpoint
app.post('/v1/chat/completions', async (req, res) => {
  try {
    const { messages, model = 'mcp-1', max_tokens = 150 } = req.body;
    
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return res.status(400).json({ error: 'Messages array is required' });
    }
    
    const lastMessage = messages[messages.length - 1];
    if (lastMessage.role !== 'user') {
      return res.status(400).json({ error: 'Last message must be from user' });
    }
    
    const userMessage = lastMessage.content;
    const tools = await getTools();
    
    if (tools.length === 0) {
      return res.status(503).json({ error: 'MCP server unavailable' });
    }
    
    // Select appropriate function using AI
    const selectedTool = await selectFunction(userMessage, tools);
    if (!selectedTool) {
      return res.status(400).json({ error: 'No suitable tool found' });
    }

    // Extract parameters using AI
    const parameters = await extractParameters(userMessage, selectedTool);
    
    // Call the tool
    const toolResult = await callTool(selectedTool.name, parameters);
    
    // Format response like OpenAI
    const response = {
      id: 'mcp-' + Date.now(),
      object: 'chat.completion',
      created: Math.floor(Date.now() / 1000),
      model: model,
      choices: [{
        index: 0,
        message: {
          role: 'assistant',
          content: formatToolResponse(selectedTool.name, toolResult.result)
        },
        finish_reason: 'stop'
      }],
      usage: {
        prompt_tokens: userMessage.length,
        completion_tokens: 50,
        total_tokens: userMessage.length + 50
      },
      mcp_tool_used: selectedTool.name,
      mcp_parameters: parameters
    };
    
    res.json(response);
    
  } catch (error) {
    console.error('Error:', error.message);
    res.status(500).json({ error: error.message });
  }
});

// Format tool response into natural language
function formatToolResponse(toolName, result) {
  // If result has an 'answer' field, it's likely an AI response
  if (result.answer) {
    return result.answer;
  }

  // Weather-like responses
  if (result.location && result.temperature) {
    return `The weather in ${result.location} is ${result.condition || 'unknown'} with a temperature of ${result.temperature}°${result.unit || 'C'}${result.humidity ? ` and ${result.humidity}% humidity` : ''}.`;
  }

  // Math calculation responses
  if (result.expression && result.result !== undefined) {
    return `The result of ${result.expression} is ${result.result}.`;
  }

  // Generic response - try to format nicely
  if (typeof result === 'object') {
    const keys = Object.keys(result);
    if (keys.length === 1) {
      return String(result[keys[0]]);
    }

    // Create a readable summary
    const summary = keys.map(key => `${key}: ${result[key]}`).join(', ');
    return summary;
  }

  return String(result);
}

// Health check
app.get('/health', async (req, res) => {
  try {
    const mcpHealth = await axios.get(`${MCP_SERVER_URL}/health`);
    res.json({ 
      status: 'ok', 
      mcp_server: mcpHealth.data,
      mcp_server_url: MCP_SERVER_URL
    });
  } catch (error) {
    res.status(503).json({ 
      status: 'error', 
      error: 'MCP server unavailable',
      mcp_server_url: MCP_SERVER_URL
    });
  }
});

// List available models (for OpenAI compatibility)
app.get('/v1/models', async (req, res) => {
  const tools = await getTools();
  res.json({
    object: 'list',
    data: [{
      id: 'mcp-1',
      object: 'model',
      created: Math.floor(Date.now() / 1000),
      owned_by: 'mcp',
      tools_available: tools.map(t => t.name)
    }]
  });
});

// Simple test endpoint
app.get('/test', async (req, res) => {
  const { q } = req.query;
  if (!q) {
    return res.json({ error: 'Add ?q=your question' });
  }

  try {
    const tools = await getTools();
    const selectedTool = await selectFunction(q, tools);
    const parameters = await extractParameters(q, selectedTool);
    const result = await callTool(selectedTool.name, parameters);

    res.json({
      question: q,
      tool_used: selectedTool.name,
      parameters,
      result: result.result,
      formatted: formatToolResponse(selectedTool.name, result.result)
    });
  } catch (error) {
    res.json({ error: error.message });
  }
});

app.listen(PORT, () => {
  console.log(`🚀 MCP Client running on port ${PORT}`);
  console.log(`🔗 OpenAI-like API: http://localhost:${PORT}/v1/chat/completions`);
  console.log(`📋 Models: http://localhost:${PORT}/v1/models`);
  console.log(`💚 Health: http://localhost:${PORT}/health`);
  console.log(`🔧 MCP Server: ${MCP_SERVER_URL}`);
});
