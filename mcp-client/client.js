import express from 'express';
import cors from 'cors';
import axios from 'axios';

const app = express();
const PORT = 8000;

// MCP Server configuration (can be on different machine)
const MCP_SERVER_URL = process.env.MCP_SERVER_URL || 'http://localhost:3000';

app.use(cors());
app.use(express.json());

// Get available tools from MCP server
async function getTools() {
  try {
    const response = await axios.get(`${MCP_SERVER_URL}/tools`);
    return response.data.tools;
  } catch (error) {
    console.error('Failed to get tools:', error.message);
    return [];
  }
}

// Call a tool on the MCP server
async function callTool(name, args) {
  try {
    const response = await axios.post(`${MCP_SERVER_URL}/tools/call`, {
      name,
      arguments: args
    });
    return response.data;
  } catch (error) {
    throw new Error(`Tool call failed: ${error.response?.data?.error || error.message}`);
  }
}

// Generic function selection based on keyword matching with tool names and descriptions
function selectFunction(message, tools) {
  const msg = message.toLowerCase();
  const words = msg.split(/\s+/);

  let bestMatch = null;
  let bestScore = 0;

  for (const tool of tools) {
    const toolName = tool.name.toLowerCase();
    const toolDesc = tool.description.toLowerCase();
    const toolText = `${toolName} ${toolDesc}`;

    let score = 0;

    // Score based on word matches
    for (const word of words) {
      if (word.length > 2) { // Skip short words
        if (toolName.includes(word)) score += 3;
        if (toolDesc.includes(word)) score += 2;
      }
    }

    // Score based on parameter names (what the tool expects)
    const params = tool.parameters?.properties || {};
    for (const paramName of Object.keys(params)) {
      if (msg.includes(paramName.toLowerCase())) {
        score += 1;
      }
    }

    if (score > bestScore) {
      bestScore = score;
      bestMatch = tool;
    }
  }

  // If no good match found, return first tool as fallback
  return bestMatch || tools[0];
}

// Generic parameter extraction based on tool schema
function extractParameters(message, tool) {
  const params = {};
  const required = tool.parameters?.required || [];
  const properties = tool.parameters?.properties || {};

  for (const [paramName, paramSchema] of Object.entries(properties)) {
    const paramDesc = paramSchema.description?.toLowerCase() || '';
    const paramType = paramSchema.type || 'string';

    let value = null;

    // Try to extract based on parameter name and description keywords
    const keywords = [paramName.toLowerCase()];
    if (paramDesc) {
      // Extract meaningful words from description
      const descWords = paramDesc.split(/\s+/).filter(word => word.length > 3);
      keywords.push(...descWords);
    }

    // Look for patterns based on parameter type and keywords
    if (paramType === 'string') {
      // For string parameters, try various extraction patterns
      for (const keyword of keywords) {
        // Pattern: "keyword value" or "keyword: value" or "keyword is value"
        const patterns = [
          new RegExp(`${keyword}\\s+(?:is\\s+|:)?([^,\\.!?]+)`, 'i'),
          new RegExp(`(?:in|for|of)\\s+([^,\\.!?]+)`, 'i'), // location-like
          new RegExp(`([^,\\.!?]+)\\s+${keyword}`, 'i') // reverse order
        ];

        for (const pattern of patterns) {
          const match = message.match(pattern);
          if (match && match[1]) {
            value = match[1].trim();
            break;
          }
        }
        if (value) break;
      }

      // If still no value and it looks like a math expression
      if (!value && (paramDesc.includes('expression') || paramDesc.includes('math') || paramDesc.includes('calculate'))) {
        const mathMatch = message.match(/(\d+[\+\-\*\/\d\s\(\)\.]+)/);
        if (mathMatch) {
          value = mathMatch[1].trim();
        }
      }

      // If still no value, use the whole message for question-like parameters
      if (!value && (paramDesc.includes('question') || paramDesc.includes('query') || paramDesc.includes('text'))) {
        value = message;
      }
    }

    // Set the parameter value
    if (value) {
      params[paramName] = value;
    } else if (required.includes(paramName)) {
      // Provide a sensible default for required parameters
      if (paramDesc.includes('location') || paramDesc.includes('city')) {
        params[paramName] = 'New York';
      } else {
        params[paramName] = message; // fallback to full message
      }
    }
  }

  return params;
}

// OpenAI-compatible chat completions endpoint
app.post('/v1/chat/completions', async (req, res) => {
  try {
    const { messages, model = 'mcp-1', max_tokens = 150 } = req.body;
    
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return res.status(400).json({ error: 'Messages array is required' });
    }
    
    const lastMessage = messages[messages.length - 1];
    if (lastMessage.role !== 'user') {
      return res.status(400).json({ error: 'Last message must be from user' });
    }
    
    const userMessage = lastMessage.content;
    const tools = await getTools();
    
    if (tools.length === 0) {
      return res.status(503).json({ error: 'MCP server unavailable' });
    }
    
    // Select appropriate function
    const selectedTool = selectFunction(userMessage, tools);
    if (!selectedTool) {
      return res.status(400).json({ error: 'No suitable tool found' });
    }
    
    // Extract parameters
    const parameters = extractParameters(userMessage, selectedTool);
    
    // Call the tool
    const toolResult = await callTool(selectedTool.name, parameters);
    
    // Format response like OpenAI
    const response = {
      id: 'mcp-' + Date.now(),
      object: 'chat.completion',
      created: Math.floor(Date.now() / 1000),
      model: model,
      choices: [{
        index: 0,
        message: {
          role: 'assistant',
          content: formatToolResponse(selectedTool.name, toolResult.result)
        },
        finish_reason: 'stop'
      }],
      usage: {
        prompt_tokens: userMessage.length,
        completion_tokens: 50,
        total_tokens: userMessage.length + 50
      },
      mcp_tool_used: selectedTool.name,
      mcp_parameters: parameters
    };
    
    res.json(response);
    
  } catch (error) {
    console.error('Error:', error.message);
    res.status(500).json({ error: error.message });
  }
});

// Format tool response into natural language
function formatToolResponse(toolName, result) {
  // If result has an 'answer' field, it's likely an AI response
  if (result.answer) {
    return result.answer;
  }

  // Weather-like responses
  if (result.location && result.temperature) {
    return `The weather in ${result.location} is ${result.condition || 'unknown'} with a temperature of ${result.temperature}°${result.unit || 'C'}${result.humidity ? ` and ${result.humidity}% humidity` : ''}.`;
  }

  // Math calculation responses
  if (result.expression && result.result !== undefined) {
    return `The result of ${result.expression} is ${result.result}.`;
  }

  // Generic response - try to format nicely
  if (typeof result === 'object') {
    const keys = Object.keys(result);
    if (keys.length === 1) {
      return String(result[keys[0]]);
    }

    // Create a readable summary
    const summary = keys.map(key => `${key}: ${result[key]}`).join(', ');
    return summary;
  }

  return String(result);
}

// Health check
app.get('/health', async (req, res) => {
  try {
    const mcpHealth = await axios.get(`${MCP_SERVER_URL}/health`);
    res.json({ 
      status: 'ok', 
      mcp_server: mcpHealth.data,
      mcp_server_url: MCP_SERVER_URL
    });
  } catch (error) {
    res.status(503).json({ 
      status: 'error', 
      error: 'MCP server unavailable',
      mcp_server_url: MCP_SERVER_URL
    });
  }
});

// List available models (for OpenAI compatibility)
app.get('/v1/models', async (req, res) => {
  const tools = await getTools();
  res.json({
    object: 'list',
    data: [{
      id: 'mcp-1',
      object: 'model',
      created: Math.floor(Date.now() / 1000),
      owned_by: 'mcp',
      tools_available: tools.map(t => t.name)
    }]
  });
});

// Simple test endpoint
app.get('/test', async (req, res) => {
  const { q } = req.query;
  if (!q) {
    return res.json({ error: 'Add ?q=your question' });
  }

  try {
    const tools = await getTools();
    const selectedTool = selectFunction(q, tools);
    const parameters = extractParameters(q, selectedTool);
    const result = await callTool(selectedTool.name, parameters);

    res.json({
      question: q,
      tool_used: selectedTool.name,
      parameters,
      result: result.result,
      formatted: formatToolResponse(selectedTool.name, result.result)
    });
  } catch (error) {
    res.json({ error: error.message });
  }
});

app.listen(PORT, () => {
  console.log(`🚀 MCP Client running on port ${PORT}`);
  console.log(`🔗 OpenAI-like API: http://localhost:${PORT}/v1/chat/completions`);
  console.log(`📋 Models: http://localhost:${PORT}/v1/models`);
  console.log(`💚 Health: http://localhost:${PORT}/health`);
  console.log(`🔧 MCP Server: ${MCP_SERVER_URL}`);
});
