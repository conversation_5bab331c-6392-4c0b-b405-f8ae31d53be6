import express from 'express';
import cors from 'cors';
import axios from 'axios';

const app = express();
const PORT = 8000;

// MCP Server configuration (can be on different machine)
const MCP_SERVER_URL = process.env.MCP_SERVER_URL || 'http://localhost:3000';

app.use(cors());
app.use(express.json());

// Get available tools from MCP server
async function getTools() {
  try {
    const response = await axios.get(`${MCP_SERVER_URL}/tools`);
    return response.data.tools;
  } catch (error) {
    console.error('Failed to get tools:', error.message);
    return [];
  }
}

// Call a tool on the MCP server
async function callTool(name, args) {
  try {
    const response = await axios.post(`${MCP_SERVER_URL}/tools/call`, {
      name,
      arguments: args
    });
    return response.data;
  } catch (error) {
    throw new Error(`Tool call failed: ${error.response?.data?.error || error.message}`);
  }
}

// ElectronHub API configuration
const ELECTRONHUB_API_BASE = 'https://api.electronhub.ai/v1';
const ELECTRONHUB_API_KEY = 'ek-proxy-R4tBYVMxBMz7cH3dz3yfg5utVNaYuugooC4vQi2hB3hd';

// Use OpenAI function calling to select and execute tools
async function handleWithFunctionCalling(message, tools) {
  // Convert MCP tools to OpenAI function format
  const openaiTools = tools.map(tool => ({
    type: "function",
    function: {
      name: tool.name,
      description: tool.description,
      parameters: tool.parameters || { type: "object", properties: {} }
    }
  }));

  try {
    const response = await axios.post(`${ELECTRONHUB_API_BASE}/chat/completions`, {
      model: 'gpt-4',
      messages: [{ role: 'user', content: message }],
      tools: openaiTools,
      tool_choice: 'auto'
    }, {
      headers: {
        'Authorization': `Bearer ${ELECTRONHUB_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    const choice = response.data.choices[0];

    if (choice.message.tool_calls && choice.message.tool_calls.length > 0) {
      const toolCall = choice.message.tool_calls[0];
      const functionName = toolCall.function.name;
      const functionArgs = JSON.parse(toolCall.function.arguments);

      // Execute the tool on MCP server
      const toolResult = await callTool(functionName, functionArgs);

      return {
        toolUsed: functionName,
        parameters: functionArgs,
        result: toolResult.result,
        aiMessage: choice.message.content || `Used ${functionName} tool`
      };
    } else {
      // No tool was called, return the AI response directly
      return {
        toolUsed: null,
        parameters: {},
        result: null,
        aiMessage: choice.message.content
      };
    }
  } catch (error) {
    console.error('Function calling failed:', error.message);
    throw error;
  }
}



// OpenAI-compatible chat completions endpoint
app.post('/v1/chat/completions', async (req, res) => {
  try {
    const { messages, model = 'mcp-1', max_tokens = 150 } = req.body;
    
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return res.status(400).json({ error: 'Messages array is required' });
    }
    
    const lastMessage = messages[messages.length - 1];
    if (lastMessage.role !== 'user') {
      return res.status(400).json({ error: 'Last message must be from user' });
    }
    
    const userMessage = lastMessage.content;
    const tools = await getTools();
    
    if (tools.length === 0) {
      return res.status(503).json({ error: 'MCP server unavailable' });
    }
    
    // Use OpenAI function calling to handle the request
    const result = await handleWithFunctionCalling(userMessage, tools);

    // Format response like OpenAI
    const response = {
      id: 'mcp-' + Date.now(),
      object: 'chat.completion',
      created: Math.floor(Date.now() / 1000),
      model: model,
      choices: [{
        index: 0,
        message: {
          role: 'assistant',
          content: result.toolUsed ?
            formatToolResponse(result.toolUsed, result.result) :
            result.aiMessage
        },
        finish_reason: 'stop'
      }],
      usage: {
        prompt_tokens: userMessage.length,
        completion_tokens: 50,
        total_tokens: userMessage.length + 50
      },
      mcp_tool_used: result.toolUsed,
      mcp_parameters: result.parameters
    };
    
    res.json(response);
    
  } catch (error) {
    console.error('Error:', error.message);
    res.status(500).json({ error: error.message });
  }
});

// Format tool response into natural language
function formatToolResponse(toolName, result) {
  // If result has an 'answer' field, it's likely an AI response
  if (result.answer) {
    return result.answer;
  }

  // Weather-like responses
  if (result.location && result.temperature) {
    return `The weather in ${result.location} is ${result.condition || 'unknown'} with a temperature of ${result.temperature}°${result.unit || 'C'}${result.humidity ? ` and ${result.humidity}% humidity` : ''}.`;
  }

  // Math calculation responses
  if (result.expression && result.result !== undefined) {
    return `The result of ${result.expression} is ${result.result}.`;
  }

  // Generic response - try to format nicely
  if (typeof result === 'object') {
    const keys = Object.keys(result);
    if (keys.length === 1) {
      return String(result[keys[0]]);
    }

    // Create a readable summary
    const summary = keys.map(key => `${key}: ${result[key]}`).join(', ');
    return summary;
  }

  return String(result);
}

// Health check
app.get('/health', async (req, res) => {
  try {
    const mcpHealth = await axios.get(`${MCP_SERVER_URL}/health`);
    res.json({ 
      status: 'ok', 
      mcp_server: mcpHealth.data,
      mcp_server_url: MCP_SERVER_URL
    });
  } catch (error) {
    res.status(503).json({ 
      status: 'error', 
      error: 'MCP server unavailable',
      mcp_server_url: MCP_SERVER_URL
    });
  }
});

// List available models (for OpenAI compatibility)
app.get('/v1/models', async (req, res) => {
  const tools = await getTools();
  res.json({
    object: 'list',
    data: [{
      id: 'mcp-1',
      object: 'model',
      created: Math.floor(Date.now() / 1000),
      owned_by: 'mcp',
      tools_available: tools.map(t => t.name)
    }]
  });
});

// Simple test endpoint
app.get('/test', async (req, res) => {
  const { q } = req.query;
  if (!q) {
    return res.json({ error: 'Add ?q=your question' });
  }

  try {
    const tools = await getTools();
    const result = await handleWithFunctionCalling(q, tools);

    res.json({
      question: q,
      tool_used: result.toolUsed,
      parameters: result.parameters,
      result: result.result,
      formatted: result.toolUsed ?
        formatToolResponse(result.toolUsed, result.result) :
        result.aiMessage
    });
  } catch (error) {
    res.json({ error: error.message });
  }
});

app.listen(PORT, () => {
  console.log(`🚀 MCP Client running on port ${PORT}`);
  console.log(`🔗 OpenAI-like API: http://localhost:${PORT}/v1/chat/completions`);
  console.log(`📋 Models: http://localhost:${PORT}/v1/models`);
  console.log(`💚 Health: http://localhost:${PORT}/health`);
  console.log(`🔧 MCP Server: ${MCP_SERVER_URL}`);
});
