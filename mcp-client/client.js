import express from 'express';
import cors from 'cors';
import axios from 'axios';

const app = express();
const PORT = 8000;

// MCP Server configuration (can be on different machine)
const MCP_SERVER_URL = process.env.MCP_SERVER_URL || 'http://localhost:3000';

app.use(cors());
app.use(express.json());

// Get available tools from MCP server
async function getTools() {
  try {
    const response = await axios.get(`${MCP_SERVER_URL}/tools`);
    return response.data.tools;
  } catch (error) {
    console.error('Failed to get tools:', error.message);
    return [];
  }
}

// Call a tool on the MCP server
async function callTool(name, args) {
  try {
    const response = await axios.post(`${MCP_SERVER_URL}/tools/call`, {
      name,
      arguments: args
    });
    return response.data;
  } catch (error) {
    throw new Error(`Tool call failed: ${error.response?.data?.error || error.message}`);
  }
}

// Simple function selection based on user message
function selectFunction(message, tools) {
  const msg = message.toLowerCase();
  
  if (msg.includes('weather') || msg.includes('temperature')) {
    return tools.find(t => t.name === 'get_weather');
  }
  
  if (msg.includes('calculate') || msg.includes('math') || /\d+[\+\-\*\/]\d+/.test(msg)) {
    return tools.find(t => t.name === 'calculate');
  }
  
  // Default to AI for everything else
  return tools.find(t => t.name === 'ask_ai');
}

// Extract parameters from user message
function extractParameters(message, tool) {
  const params = {};
  
  if (tool.name === 'get_weather') {
    const locationMatch = message.match(/weather (?:in|for) ([^?.,!]+)/i) || 
                         message.match(/([A-Za-z\s]+) weather/i);
    if (locationMatch) {
      params.location = locationMatch[1].trim();
    } else {
      params.location = 'New York'; // default
    }
  }
  
  if (tool.name === 'calculate') {
    const mathMatch = message.match(/calculate\s+(.+)|what\s+is\s+(.+)|(\d+[\+\-\*\/\d\s\(\)]+)/i);
    if (mathMatch) {
      params.expression = (mathMatch[1] || mathMatch[2] || mathMatch[3]).trim();
    }
  }
  
  if (tool.name === 'ask_ai') {
    params.question = message;
  }
  
  return params;
}

// OpenAI-compatible chat completions endpoint
app.post('/v1/chat/completions', async (req, res) => {
  try {
    const { messages, model = 'mcp-1', max_tokens = 150 } = req.body;
    
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return res.status(400).json({ error: 'Messages array is required' });
    }
    
    const lastMessage = messages[messages.length - 1];
    if (lastMessage.role !== 'user') {
      return res.status(400).json({ error: 'Last message must be from user' });
    }
    
    const userMessage = lastMessage.content;
    const tools = await getTools();
    
    if (tools.length === 0) {
      return res.status(503).json({ error: 'MCP server unavailable' });
    }
    
    // Select appropriate function
    const selectedTool = selectFunction(userMessage, tools);
    if (!selectedTool) {
      return res.status(400).json({ error: 'No suitable tool found' });
    }
    
    // Extract parameters
    const parameters = extractParameters(userMessage, selectedTool);
    
    // Call the tool
    const toolResult = await callTool(selectedTool.name, parameters);
    
    // Format response like OpenAI
    const response = {
      id: 'mcp-' + Date.now(),
      object: 'chat.completion',
      created: Math.floor(Date.now() / 1000),
      model: model,
      choices: [{
        index: 0,
        message: {
          role: 'assistant',
          content: formatToolResponse(selectedTool.name, toolResult.result)
        },
        finish_reason: 'stop'
      }],
      usage: {
        prompt_tokens: userMessage.length,
        completion_tokens: 50,
        total_tokens: userMessage.length + 50
      },
      mcp_tool_used: selectedTool.name,
      mcp_parameters: parameters
    };
    
    res.json(response);
    
  } catch (error) {
    console.error('Error:', error.message);
    res.status(500).json({ error: error.message });
  }
});

// Format tool response into natural language
function formatToolResponse(toolName, result) {
  switch (toolName) {
    case 'get_weather':
      return `The weather in ${result.location} is ${result.condition} with a temperature of ${result.temperature}°C and ${result.humidity}% humidity.`;
    
    case 'calculate':
      return `The result of ${result.expression} is ${result.result}.`;
    
    case 'ask_ai':
      return result.answer;
    
    default:
      return JSON.stringify(result);
  }
}

// Health check
app.get('/health', async (req, res) => {
  try {
    const mcpHealth = await axios.get(`${MCP_SERVER_URL}/health`);
    res.json({ 
      status: 'ok', 
      mcp_server: mcpHealth.data,
      mcp_server_url: MCP_SERVER_URL
    });
  } catch (error) {
    res.status(503).json({ 
      status: 'error', 
      error: 'MCP server unavailable',
      mcp_server_url: MCP_SERVER_URL
    });
  }
});

// List available models (for OpenAI compatibility)
app.get('/v1/models', async (req, res) => {
  const tools = await getTools();
  res.json({
    object: 'list',
    data: [{
      id: 'mcp-1',
      object: 'model',
      created: Math.floor(Date.now() / 1000),
      owned_by: 'mcp',
      tools_available: tools.map(t => t.name)
    }]
  });
});

app.listen(PORT, () => {
  console.log(`🚀 MCP Client running on port ${PORT}`);
  console.log(`🔗 OpenAI-like API: http://localhost:${PORT}/v1/chat/completions`);
  console.log(`📋 Models: http://localhost:${PORT}/v1/models`);
  console.log(`💚 Health: http://localhost:${PORT}/health`);
  console.log(`🔧 MCP Server: ${MCP_SERVER_URL}`);
});
