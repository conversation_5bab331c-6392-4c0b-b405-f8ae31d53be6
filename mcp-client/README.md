# MCP Client

OpenAI-compatible API client that connects to MCP server.

## Setup

```bash
npm install
npm start
```

Client runs on port 8000.

## Configuration

Set MCP server URL (default: http://localhost:3000):
```bash
export MCP_SERVER_URL=http://your-server:3000
npm start
```

## OpenAI-Compatible API

Use like OpenAI API:

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "mcp-1",
    "messages": [{"role": "user", "content": "What is the weather in Paris?"}]
  }'
```

## Endpoints

- `POST /v1/chat/completions` - Chat completions (OpenAI-like)
- `GET /v1/models` - List models
- `GET /health` - Health check

## Examples

```bash
# Weather
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"messages": [{"role": "user", "content": "weather in Tokyo"}]}'

# Math
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"messages": [{"role": "user", "content": "calculate 2 + 2"}]}'

# AI Question
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"messages": [{"role": "user", "content": "explain quantum computing"}]}'
```
