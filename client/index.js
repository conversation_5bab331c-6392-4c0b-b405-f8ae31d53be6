import axios from 'axios';
import { MCPError, MCPResponseStatus } from '../shared/types.js';

class MCPClient {
  constructor() {
    this.servers = new Map();
    this.defaultTimeout = 10000;
  }

  async addServer(name, baseUrl, options = {}) {
    const serverConfig = {
      name,
      baseUrl: baseUrl.replace(/\/$/, ''),
      timeout: options.timeout || this.defaultTimeout,
      headers: options.headers || {},
      ...options
    };

    try {
      const response = await this.makeRequest(serverConfig, '/server/info');
      serverConfig.info = response.data.data;

      this.servers.set(name, serverConfig);
      console.log(`✅ Connected to MCP server: ${name} (${serverConfig.info.name})`);

      return serverConfig.info;
    } catch (error) {
      throw new MCPError(
        `Failed to connect to server ${name}: ${error.message}`,
        'CONNECTION_FAILED',
        { serverName: name, baseUrl }
      );
    }
  }

  removeServer(name) {
    const removed = this.servers.delete(name);
    if (removed) {
      console.log(`🗑️  Removed server: ${name}`);
    }
    return removed;
  }

  getServers() {
    return Array.from(this.servers.entries()).map(([name, config]) => ({
      name,
      baseUrl: config.baseUrl,
      info: config.info
    }));
  }

  async getServerTools(serverName) {
    const server = this.servers.get(serverName);
    if (!server) {
      throw new MCPError(`Server ${serverName} not found`, 'SERVER_NOT_FOUND');
    }

    try {
      const response = await this.makeRequest(server, '/tools/list');
      return response.data.data.tools || [];
    } catch (error) {
      throw new MCPError(
        `Failed to get tools from ${serverName}: ${error.message}`,
        'TOOLS_FETCH_FAILED',
        { serverName }
      );
    }
  }

  async getAllTools() {
    const allTools = {};
    
    for (const [serverName, server] of this.servers) {
      try {
        const tools = await this.getServerTools(serverName);
        allTools[serverName] = tools;
      } catch (error) {
        console.error(`Failed to get tools from ${serverName}:`, error.message);
        allTools[serverName] = [];
      }
    }
    
    return allTools;
  }

  async callTool(serverName, toolName, args = {}) {
    const server = this.servers.get(serverName);
    if (!server) {
      throw new MCPError(`Server ${serverName} not found`, 'SERVER_NOT_FOUND');
    }

    try {
      console.log(`🔧 Calling ${serverName}.${toolName} with args:`, args);
      
      const response = await this.makeRequest(server, '/tools/call', {
        method: 'POST',
        data: {
          name: toolName,
          arguments: args
        }
      });

      console.log(`✅ Tool call successful:`, response.data);
      return response.data;
    } catch (error) {
      throw new MCPError(
        `Tool call failed: ${error.message}`,
        'TOOL_CALL_FAILED',
        { serverName, toolName, args }
      );
    }
  }

  async findAndCallTool(toolName, args = {}, preferredServer = null) {
    if (preferredServer && this.servers.has(preferredServer)) {
      try {
        return await this.callTool(preferredServer, toolName, args);
      } catch (error) {
        console.warn(`Failed to call ${toolName} on preferred server ${preferredServer}, trying others...`);
      }
    }

    const allTools = await this.getAllTools();
    
    for (const [serverName, tools] of Object.entries(allTools)) {
      const tool = tools.find(t => t.name === toolName);
      if (tool) {
        try {
          return await this.callTool(serverName, toolName, args);
        } catch (error) {
          console.warn(`Failed to call ${toolName} on ${serverName}:`, error.message);
          continue;
        }
      }
    }

    throw new MCPError(
      `Tool ${toolName} not found on any connected server`,
      'TOOL_NOT_FOUND',
      { toolName, availableServers: Array.from(this.servers.keys()) }
    );
  }

  async simulateOpenAIFunctionCalling(messages, availableTools = null) {
    console.log('\n🤖 OpenAI-style Function Calling Simulation');
    
    const lastMessage = messages[messages.length - 1];
    if (!lastMessage || lastMessage.role !== 'user') {
      throw new MCPError('Last message must be from user', 'INVALID_MESSAGE');
    }

    const userQuery = lastMessage.content;
    console.log(`📝 User query: ${userQuery}`);

    let tools = availableTools;
    if (!tools) {
      const allTools = await this.getAllTools();
      tools = Object.entries(allTools).flatMap(([serverName, serverTools]) =>
        serverTools.map(tool => ({ ...tool, serverName }))
      );
    }

    const selectedTool = this.selectToolForQuery(userQuery, tools);
    
    if (!selectedTool) {
      return {
        role: 'assistant',
        content: 'I cannot determine which function to call for this query.',
        available_tools: tools.map(t => t.name)
      };
    }

    console.log(`🎯 Selected tool: ${selectedTool.serverName}.${selectedTool.name}`);

    const parameters = this.extractParametersFromQuery(userQuery, selectedTool);
    console.log(`📋 Extracted parameters:`, parameters);

    try {
      const result = await this.callTool(selectedTool.serverName, selectedTool.name, parameters);
      
      return {
        role: 'assistant',
        content: `I called the ${selectedTool.name} function and got the following result: ${JSON.stringify(result.data, null, 2)}`,
        function_call: {
          name: selectedTool.name,
          arguments: JSON.stringify(parameters),
          server: selectedTool.serverName
        },
        function_result: result
      };
    } catch (error) {
      return {
        role: 'assistant',
        content: `Error calling function ${selectedTool.name}: ${error.message}`,
        function_call: {
          name: selectedTool.name,
          arguments: JSON.stringify(parameters),
          server: selectedTool.serverName
        },
        error: error.message
      };
    }
  }

  selectToolForQuery(query, tools) {
    const queryLower = query.toLowerCase();
    
    const keywords = {
      weather: ['weather', 'temperature', 'forecast'],
      calculate: ['calculate', 'math', 'compute', 'solve'],
      uuid: ['uuid', 'id', 'identifier'],
      echo: ['echo', 'repeat', 'say'],
      time: ['time', 'date', 'now'],
      system: ['system', 'info', 'server']
    };

    for (const tool of tools) {
      const toolName = tool.name.toLowerCase();
      
      for (const [category, words] of Object.entries(keywords)) {
        if (toolName.includes(category)) {
          for (const word of words) {
            if (queryLower.includes(word)) {
              return tool;
            }
          }
        }
      }
    }
    
    return tools[0] || null;
  }

  extractParametersFromQuery(query, toolDef) {
    const parameters = {};
    const queryLower = query.toLowerCase();
    
    if (toolDef.name.includes('weather')) {
      const locationMatch = query.match(/(?:weather (?:in|for) |in )([^,\n]+)/i);
      if (locationMatch) {
        parameters.location = locationMatch[1].trim();
      }
    }
    
    if (toolDef.name.includes('calculate')) {
      const mathMatch = query.match(/calculate\s+(.+)|what\s+is\s+(.+)|solve\s+(.+)/i);
      if (mathMatch) {
        parameters.expression = (mathMatch[1] || mathMatch[2] || mathMatch[3]).trim();
      }
    }
    
    if (toolDef.name.includes('echo')) {
      const echoMatch = query.match(/echo\s+(.+)|say\s+(.+)|repeat\s+(.+)/i);
      if (echoMatch) {
        parameters.message = (echoMatch[1] || echoMatch[2] || echoMatch[3]).trim();
      } else {
        parameters.message = query;
      }
    }
    
    return parameters;
  }

  async makeRequest(server, endpoint, options = {}) {
    const config = {
      method: 'GET',
      url: `${server.baseUrl}${endpoint}`,
      timeout: server.timeout,
      headers: {
        'Content-Type': 'application/json',
        ...server.headers
      },
      ...options
    };

    try {
      const response = await axios(config);
      
      if (response.data.status === MCPResponseStatus.ERROR) {
        throw new MCPError(
          response.data.error.message,
          response.data.error.code,
          response.data.error.details
        );
      }
      
      return response;
    } catch (error) {
      if (error.response) {
        const errorData = error.response.data;
        if (errorData && errorData.error) {
          throw new MCPError(
            errorData.error.message,
            errorData.error.code,
            errorData.error.details
          );
        }
      }
      
      throw new MCPError(
        error.message || 'Request failed',
        'REQUEST_FAILED',
        { endpoint, status: error.response?.status }
      );
    }
  }
}

export default MCPClient;
