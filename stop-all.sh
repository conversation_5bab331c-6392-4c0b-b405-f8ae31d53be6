#!/bin/bash

echo "🛑 Stopping all MCP servers..."

# Function to stop a server
stop_server() {
    local name=$1
    local pid_file=".${name}_server.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            echo "Stopping $name server (PID: $pid)..."
            kill "$pid"
            rm "$pid_file"
            echo "✅ $name server stopped"
        else
            echo "⚠️  $name server was not running"
            rm "$pid_file"
        fi
    else
        echo "⚠️  No PID file found for $name server"
    fi
}

# Stop all servers
stop_server "default"
stop_server "weather"
stop_server "math"
stop_server "external-api"

# Also kill any remaining node processes running our servers
pkill -f "server/index.js"
pkill -f "weather-server.js"
pkill -f "math-server.js"
pkill -f "external-api-server.js"

echo ""
echo "✅ All servers stopped"
