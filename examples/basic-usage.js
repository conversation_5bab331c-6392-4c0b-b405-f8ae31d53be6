import MCPFunctionClient from '../src/client.js';

async function basicExample() {
  console.log('🔧 Basic MCP Function Calling Example\n');
  
  const client = new MCPFunctionClient();
  
  try {
    await client.connect();
    
    console.log('Available functions:');
    const tools = client.getAvailableTools();
    tools.forEach(tool => {
      console.log(`- ${tool.name}: ${tool.description}`);
    });
    
    console.log('\n--- Direct Function Calls ---');
    
    const weatherResult = await client.callFunction('get_weather', {
      location: 'London, UK',
      unit: 'celsius'
    });
    console.log('Weather:', weatherResult);
    
    const calcResult = await client.callFunction('calculate', {
      expression: 'sqrt(144) + 5'
    });
    console.log('Calculation:', calcResult);
    
    console.log('\n--- OpenAI-style Function Calling ---');
    
    const conversation = [
      { role: 'user', content: 'What is the weather in Tokyo?' }
    ];
    
    const response = await client.simulateOpenAIFunctionCalling(conversation);
    console.log('AI Response:', response);
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await client.disconnect();
  }
}

basicExample();
