import MCPServer from '../server/index.js';
import axios from 'axios';

const externalApiServer = new MCPServer({
  name: 'External API MCP Server',
  description: 'MCP server that integrates with external APIs',
  port: 3003
});

externalApiServer.registerTool({
  name: 'call_external_api',
  description: 'Make calls to external APIs with authentication',
  parameters: {
    properties: {
      url: {
        type: 'string',
        description: 'The API endpoint URL'
      },
      method: {
        type: 'string',
        enum: ['GET', 'POST', 'PUT', 'DELETE'],
        description: 'HTTP method',
        default: 'GET'
      },
      headers: {
        type: 'object',
        description: 'HTTP headers to include'
      },
      data: {
        type: 'object',
        description: 'Request body data for POST/PUT requests'
      },
      auth_token: {
        type: 'string',
        description: 'Authentication token'
      }
    },
    required: ['url']
  },
  handler: async (args) => {
    const { url, method = 'GET', headers = {}, data, auth_token } = args;
    
    try {
      const config = {
        method: method.toLowerCase(),
        url,
        headers: {
          'Content-Type': 'application/json',
          ...headers
        }
      };
      
      if (auth_token) {
        config.headers['Authorization'] = `Bearer ${auth_token}`;
      }
      
      if (data && (method === 'POST' || method === 'PUT')) {
        config.data = data;
      }
      
      const response = await axios(config);
      
      return {
        success: true,
        status: response.status,
        headers: response.headers,
        data: response.data,
        url: url,
        method: method,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        status: error.response?.status,
        data: error.response?.data,
        url: url,
        method: method,
        timestamp: new Date().toISOString()
      };
    }
  }
});

externalApiServer.registerTool({
  name: 'openai_chat_completion',
  description: 'Make OpenAI-compatible chat completion requests',
  parameters: {
    properties: {
      api_base: {
        type: 'string',
        description: 'API base URL (e.g., https://api.electronhub.ai/v1)',
        default: 'https://api.openai.com/v1'
      },
      api_key: {
        type: 'string',
        description: 'API key for authentication'
      },
      model: {
        type: 'string',
        description: 'Model to use',
        default: 'gpt-3.5-turbo'
      },
      messages: {
        type: 'array',
        description: 'Array of message objects',
        items: {
          type: 'object',
          properties: {
            role: { type: 'string', enum: ['system', 'user', 'assistant'] },
            content: { type: 'string' }
          }
        }
      },
      max_tokens: {
        type: 'number',
        description: 'Maximum tokens to generate',
        default: 150
      },
      temperature: {
        type: 'number',
        description: 'Temperature for randomness',
        default: 0.7
      }
    },
    required: ['api_key', 'messages']
  },
  handler: async (args) => {
    const { 
      api_base = 'https://api.openai.com/v1',
      api_key,
      model = 'gpt-3.5-turbo',
      messages,
      max_tokens = 150,
      temperature = 0.7
    } = args;
    
    try {
      const response = await axios.post(`${api_base}/chat/completions`, {
        model,
        messages,
        max_tokens,
        temperature
      }, {
        headers: {
          'Authorization': `Bearer ${api_key}`,
          'Content-Type': 'application/json'
        }
      });
      
      return {
        success: true,
        response: response.data,
        usage: response.data.usage,
        message: response.data.choices[0]?.message,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        status: error.response?.status,
        details: error.response?.data,
        timestamp: new Date().toISOString()
      };
    }
  }
});

externalApiServer.registerTool({
  name: 'proxy_request',
  description: 'Proxy requests through the MCP server with custom headers',
  parameters: {
    properties: {
      target_url: {
        type: 'string',
        description: 'Target URL to proxy to'
      },
      method: {
        type: 'string',
        enum: ['GET', 'POST', 'PUT', 'DELETE'],
        default: 'GET'
      },
      proxy_headers: {
        type: 'object',
        description: 'Headers to add/modify in the proxied request'
      },
      body: {
        type: 'object',
        description: 'Request body for POST/PUT'
      },
      timeout: {
        type: 'number',
        description: 'Request timeout in milliseconds',
        default: 10000
      }
    },
    required: ['target_url']
  },
  handler: async (args) => {
    const { 
      target_url, 
      method = 'GET', 
      proxy_headers = {}, 
      body, 
      timeout = 10000 
    } = args;
    
    try {
      const config = {
        method: method.toLowerCase(),
        url: target_url,
        timeout,
        headers: {
          'User-Agent': 'MCP-Proxy-Server/1.0',
          ...proxy_headers
        }
      };
      
      if (body && (method === 'POST' || method === 'PUT')) {
        config.data = body;
        config.headers['Content-Type'] = 'application/json';
      }
      
      const startTime = Date.now();
      const response = await axios(config);
      const endTime = Date.now();
      
      return {
        success: true,
        status: response.status,
        headers: response.headers,
        data: response.data,
        response_time_ms: endTime - startTime,
        proxied_url: target_url,
        method: method,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        status: error.response?.status,
        response_data: error.response?.data,
        proxied_url: target_url,
        method: method,
        timestamp: new Date().toISOString()
      };
    }
  }
});

console.log('🌐 Starting External API MCP Server...');
externalApiServer.start().catch(console.error);

process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down External API MCP Server...');
  await externalApiServer.stop();
  process.exit(0);
});
