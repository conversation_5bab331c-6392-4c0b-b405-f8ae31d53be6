import MCPServer from '../server/index.js';

const mathServer = new MCPServer({
  name: 'Math MCP Server',
  description: 'Specialized MCP server for mathematical operations',
  port: 3002
});

mathServer.registerTool({
  name: 'calculate',
  description: 'Perform mathematical calculations',
  parameters: {
    properties: {
      expression: {
        type: 'string',
        description: 'Mathematical expression to evaluate (e.g., "2 + 2", "sqrt(16)")'
      }
    },
    required: ['expression']
  },
  handler: async (args) => {
    const { expression } = args;
    
    try {
      const sanitizedExpression = expression
        .replace(/[^0-9+\-*/().\s]/g, '')
        .replace(/\s+/g, ' ')
        .trim();
      
      if (!sanitizedExpression) {
        throw new Error('Invalid expression after sanitization');
      }
      
      const result = eval(sanitizedExpression);
      
      if (!isFinite(result)) {
        throw new Error('Result is not a finite number');
      }
      
      return {
        success: true,
        expression: expression,
        sanitized_expression: sanitizedExpression,
        result: result,
        type: typeof result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw new Error(`Calculation failed: ${error.message}`);
    }
  }
});

mathServer.registerTool({
  name: 'advanced_math',
  description: 'Perform advanced mathematical operations',
  parameters: {
    properties: {
      operation: {
        type: 'string',
        enum: ['factorial', 'fibonacci', 'prime_check', 'gcd', 'lcm'],
        description: 'Type of advanced operation to perform'
      },
      numbers: {
        type: 'array',
        items: { type: 'number' },
        description: 'Array of numbers for the operation'
      }
    },
    required: ['operation', 'numbers']
  },
  handler: async (args) => {
    const { operation, numbers } = args;
    
    if (!Array.isArray(numbers) || numbers.length === 0) {
      throw new Error('Numbers array is required and must not be empty');
    }
    
    let result;
    
    switch (operation) {
      case 'factorial':
        if (numbers.length !== 1 || numbers[0] < 0 || !Number.isInteger(numbers[0])) {
          throw new Error('Factorial requires exactly one non-negative integer');
        }
        result = factorial(numbers[0]);
        break;
        
      case 'fibonacci':
        if (numbers.length !== 1 || numbers[0] < 0 || !Number.isInteger(numbers[0])) {
          throw new Error('Fibonacci requires exactly one non-negative integer');
        }
        result = fibonacci(numbers[0]);
        break;
        
      case 'prime_check':
        if (numbers.length !== 1 || !Number.isInteger(numbers[0])) {
          throw new Error('Prime check requires exactly one integer');
        }
        result = isPrime(numbers[0]);
        break;
        
      case 'gcd':
        if (numbers.length < 2) {
          throw new Error('GCD requires at least two numbers');
        }
        result = numbers.reduce(gcd);
        break;
        
      case 'lcm':
        if (numbers.length < 2) {
          throw new Error('LCM requires at least two numbers');
        }
        result = numbers.reduce(lcm);
        break;
        
      default:
        throw new Error(`Unknown operation: ${operation}`);
    }
    
    return {
      success: true,
      operation,
      input: numbers,
      result,
      timestamp: new Date().toISOString()
    };
  }
});

mathServer.registerTool({
  name: 'statistics',
  description: 'Calculate statistical measures for a dataset',
  parameters: {
    properties: {
      data: {
        type: 'array',
        items: { type: 'number' },
        description: 'Array of numbers to analyze'
      },
      measures: {
        type: 'array',
        items: {
          type: 'string',
          enum: ['mean', 'median', 'mode', 'std_dev', 'variance', 'min', 'max', 'range']
        },
        description: 'Statistical measures to calculate',
        default: ['mean', 'median', 'std_dev']
      }
    },
    required: ['data']
  },
  handler: async (args) => {
    const { data, measures = ['mean', 'median', 'std_dev'] } = args;
    
    if (!Array.isArray(data) || data.length === 0) {
      throw new Error('Data array is required and must not be empty');
    }
    
    const stats = {};
    const sortedData = [...data].sort((a, b) => a - b);
    
    if (measures.includes('mean')) {
      stats.mean = data.reduce((sum, val) => sum + val, 0) / data.length;
    }
    
    if (measures.includes('median')) {
      const mid = Math.floor(data.length / 2);
      stats.median = data.length % 2 === 0 
        ? (sortedData[mid - 1] + sortedData[mid]) / 2 
        : sortedData[mid];
    }
    
    if (measures.includes('mode')) {
      const frequency = {};
      data.forEach(val => frequency[val] = (frequency[val] || 0) + 1);
      const maxFreq = Math.max(...Object.values(frequency));
      stats.mode = Object.keys(frequency).filter(key => frequency[key] === maxFreq).map(Number);
    }
    
    if (measures.includes('min')) {
      stats.min = Math.min(...data);
    }
    
    if (measures.includes('max')) {
      stats.max = Math.max(...data);
    }
    
    if (measures.includes('range')) {
      stats.range = Math.max(...data) - Math.min(...data);
    }
    
    if (measures.includes('variance') || measures.includes('std_dev')) {
      const mean = stats.mean || data.reduce((sum, val) => sum + val, 0) / data.length;
      const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / data.length;
      
      if (measures.includes('variance')) {
        stats.variance = variance;
      }
      
      if (measures.includes('std_dev')) {
        stats.std_dev = Math.sqrt(variance);
      }
    }
    
    return {
      success: true,
      data_size: data.length,
      statistics: stats,
      timestamp: new Date().toISOString()
    };
  }
});

function factorial(n) {
  if (n === 0 || n === 1) return 1;
  return n * factorial(n - 1);
}

function fibonacci(n) {
  if (n <= 1) return n;
  let a = 0, b = 1;
  for (let i = 2; i <= n; i++) {
    [a, b] = [b, a + b];
  }
  return b;
}

function isPrime(n) {
  if (n < 2) return false;
  if (n === 2) return true;
  if (n % 2 === 0) return false;
  for (let i = 3; i <= Math.sqrt(n); i += 2) {
    if (n % i === 0) return false;
  }
  return true;
}

function gcd(a, b) {
  return b === 0 ? a : gcd(b, a % b);
}

function lcm(a, b) {
  return Math.abs(a * b) / gcd(a, b);
}

console.log('🔢 Starting Math MCP Server...');
mathServer.start().catch(console.error);

process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down Math MCP Server...');
  await mathServer.stop();
  process.exit(0);
});
