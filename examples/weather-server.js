import MCPServer from '../server/index.js';

const weatherServer = new MCPServer({
  name: 'Weather MCP Server',
  description: 'Specialized MCP server for weather-related functions',
  port: 3001
});

weatherServer.registerTool({
  name: 'get_weather',
  description: 'Get current weather information for a location',
  parameters: {
    properties: {
      location: {
        type: 'string',
        description: 'The city and state/country, e.g. San Francisco, CA'
      },
      unit: {
        type: 'string',
        enum: ['celsius', 'fahrenheit'],
        description: 'Temperature unit',
        default: 'fahrenheit'
      }
    },
    required: ['location']
  },
  handler: async (args) => {
    const { location, unit = 'fahrenheit' } = args;
    
    const mockWeatherData = {
      location,
      temperature: unit === 'celsius' ? Math.round(Math.random() * 30 + 5) : Math.round(Math.random() * 50 + 40),
      unit,
      condition: ['sunny', 'cloudy', 'rainy', 'snowy'][Math.floor(Math.random() * 4)],
      humidity: Math.round(Math.random() * 40 + 30),
      wind_speed: Math.round(Math.random() * 20 + 5),
      timestamp: new Date().toISOString()
    };

    return {
      success: true,
      weather: mockWeatherData,
      source: 'mock_weather_api'
    };
  }
});

weatherServer.registerTool({
  name: 'get_forecast',
  description: 'Get weather forecast for multiple days',
  parameters: {
    properties: {
      location: {
        type: 'string',
        description: 'The city and state/country'
      },
      days: {
        type: 'number',
        description: 'Number of days to forecast (1-7)',
        minimum: 1,
        maximum: 7,
        default: 3
      },
      unit: {
        type: 'string',
        enum: ['celsius', 'fahrenheit'],
        description: 'Temperature unit',
        default: 'fahrenheit'
      }
    },
    required: ['location']
  },
  handler: async (args) => {
    const { location, days = 3, unit = 'fahrenheit' } = args;
    
    const forecast = [];
    const conditions = ['sunny', 'cloudy', 'rainy', 'snowy', 'partly-cloudy'];
    
    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(date.getDate() + i);
      
      forecast.push({
        date: date.toISOString().split('T')[0],
        high: unit === 'celsius' ? Math.round(Math.random() * 25 + 10) : Math.round(Math.random() * 45 + 50),
        low: unit === 'celsius' ? Math.round(Math.random() * 15 + 0) : Math.round(Math.random() * 30 + 30),
        condition: conditions[Math.floor(Math.random() * conditions.length)],
        humidity: Math.round(Math.random() * 40 + 30),
        wind_speed: Math.round(Math.random() * 20 + 5)
      });
    }

    return {
      success: true,
      location,
      unit,
      forecast,
      source: 'mock_weather_api'
    };
  }
});

weatherServer.registerTool({
  name: 'get_air_quality',
  description: 'Get air quality information for a location',
  parameters: {
    properties: {
      location: {
        type: 'string',
        description: 'The city and state/country'
      }
    },
    required: ['location']
  },
  handler: async (args) => {
    const { location } = args;
    
    const aqi = Math.round(Math.random() * 200 + 10);
    let category, description;
    
    if (aqi <= 50) {
      category = 'Good';
      description = 'Air quality is satisfactory';
    } else if (aqi <= 100) {
      category = 'Moderate';
      description = 'Air quality is acceptable';
    } else if (aqi <= 150) {
      category = 'Unhealthy for Sensitive Groups';
      description = 'Members of sensitive groups may experience health effects';
    } else if (aqi <= 200) {
      category = 'Unhealthy';
      description = 'Everyone may begin to experience health effects';
    } else {
      category = 'Very Unhealthy';
      description = 'Health warnings of emergency conditions';
    }

    return {
      success: true,
      location,
      air_quality: {
        aqi,
        category,
        description,
        pollutants: {
          pm25: Math.round(Math.random() * 50 + 5),
          pm10: Math.round(Math.random() * 100 + 10),
          ozone: Math.round(Math.random() * 150 + 20),
          no2: Math.round(Math.random() * 80 + 10)
        }
      },
      timestamp: new Date().toISOString()
    };
  }
});

console.log('🌤️  Starting Weather MCP Server...');
weatherServer.start().catch(console.error);

process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down Weather MCP Server...');
  await weatherServer.stop();
  process.exit(0);
});
