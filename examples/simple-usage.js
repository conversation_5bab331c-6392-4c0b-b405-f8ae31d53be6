import MCPClient from '../client/index.js';

async function simpleExample() {
  console.log('🚀 Simple MCP Multi-Server Example\n');
  
  const client = new MCPClient();
  
  try {
    console.log('Connecting to servers...');
    await client.addServer('weather', 'http://localhost:3001');
    await client.addServer('math', 'http://localhost:3002');
    
    console.log('\n--- Direct Tool Calls ---');
    
    const weather = await client.callTool('weather', 'get_weather', {
      location: 'Paris, France',
      unit: 'celsius'
    });
    console.log('Weather in Paris:', weather.data.data.weather);
    
    const calculation = await client.callTool('math', 'calculate', {
      expression: '(10 + 5) * 2'
    });
    console.log('Calculation result:', calculation.data.data.result);
    
    console.log('\n--- Smart Tool Discovery ---');
    
    const autoWeather = await client.findAndCallTool('get_forecast', {
      location: 'Tokyo, Japan',
      days: 3
    });
    console.log('Tokyo forecast:', autoWeather.data.data.forecast.length, 'days');
    
    const autoMath = await client.findAndCallTool('advanced_math', {
      operation: 'fibonacci',
      numbers: [10]
    });
    console.log('Fibonacci(10):', autoMath.data.data.result);
    
    console.log('\n--- OpenAI-style Function Calling ---');
    
    const queries = [
      'What is the weather in London?',
      'Calculate the square root of 144',
      'What is the factorial of 5?'
    ];
    
    for (const query of queries) {
      console.log(`\nQuery: "${query}"`);
      const response = await client.simulateOpenAIFunctionCalling([
        { role: 'user', content: query }
      ]);
      
      console.log(`Response: ${response.content.substring(0, 100)}...`);
      if (response.function_call) {
        console.log(`Used: ${response.function_call.server}.${response.function_call.name}`);
      }
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  }
  
  console.log('\n✅ Example completed');
}

if (import.meta.url === `file://${process.argv[1]}`) {
  simpleExample().catch(console.error);
}
