import MCPClient from '../client/index.js';

async function electronHubExample() {
  console.log('🔌 ElectronHub API Integration Example\n');
  
  const client = new MCPClient();
  
  // Your ElectronHub API configuration
  const ELECTRONHUB_API_BASE = 'https://api.electronhub.ai/v1';
  const ELECTRONHUB_API_KEY = 'ek-proxy-R4tBYVMxBMz7cH3dz3yfg5utVNaYuugooC4vQi2hB3hd';
  
  try {
    console.log('Connecting to MCP servers...');
    
    // Connect to our external API server
    await client.addServer('external-api', 'http://localhost:3003');
    
    // Also connect to other servers for comparison
    await client.addServer('math', 'http://localhost:3002');
    
    console.log('\n--- Testing ElectronHub API Integration ---\n');
    
    // Example 1: Direct API call using the external API server
    console.log('1. Making direct API call to ElectronHub...');
    const directApiCall = await client.callTool('external-api', 'call_external_api', {
      url: `${ELECTRONHUB_API_BASE}/chat/completions`,
      method: 'POST',
      auth_token: ELECTRONHUB_API_KEY,
      data: {
        model: 'gpt-3.5-turbo',
        messages: [
          { role: 'user', content: 'What is the capital of France?' }
        ],
        max_tokens: 100
      }
    });
    
    if (directApiCall.data.data.success) {
      console.log('✅ API call successful!');
      console.log('Response:', directApiCall.data.data.data.choices[0]?.message?.content);
    } else {
      console.log('❌ API call failed:', directApiCall.data.data.error);
    }
    
    console.log('\n2. Using OpenAI-compatible wrapper...');
    const openaiCall = await client.callTool('external-api', 'openai_chat_completion', {
      api_base: ELECTRONHUB_API_BASE,
      api_key: ELECTRONHUB_API_KEY,
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'user', content: 'Explain quantum computing in simple terms' }
      ],
      max_tokens: 150,
      temperature: 0.7
    });
    
    if (openaiCall.data.data.success) {
      console.log('✅ OpenAI-compatible call successful!');
      console.log('Response:', openaiCall.data.data.message?.content);
      console.log('Usage:', openaiCall.data.data.usage);
    } else {
      console.log('❌ OpenAI-compatible call failed:', openaiCall.data.data.error);
    }
    
    console.log('\n3. Using proxy request...');
    const proxyCall = await client.callTool('external-api', 'proxy_request', {
      target_url: `${ELECTRONHUB_API_BASE}/models`,
      method: 'GET',
      proxy_headers: {
        'Authorization': `Bearer ${ELECTRONHUB_API_KEY}`
      }
    });
    
    if (proxyCall.data.data.success) {
      console.log('✅ Proxy call successful!');
      console.log('Available models:', proxyCall.data.data.data.data?.slice(0, 3).map(m => m.id));
      console.log('Response time:', proxyCall.data.data.response_time_ms, 'ms');
    } else {
      console.log('❌ Proxy call failed:', proxyCall.data.data.error);
    }
    
    console.log('\n--- Combining Local and External APIs ---\n');
    
    // Example: Use local math server and external AI for a complex query
    console.log('4. Combining local math with external AI...');
    
    // First, do some math locally
    const mathResult = await client.callTool('math', 'calculate', {
      expression: '(15 * 8) + (22 / 2) - 7'
    });
    
    const calculationResult = mathResult.data.data.result;
    console.log('Local calculation result:', calculationResult);
    
    // Then ask AI to explain the calculation
    const aiExplanation = await client.callTool('external-api', 'openai_chat_completion', {
      api_base: ELECTRONHUB_API_BASE,
      api_key: ELECTRONHUB_API_KEY,
      model: 'gpt-3.5-turbo',
      messages: [
        { 
          role: 'user', 
          content: `I calculated (15 * 8) + (22 / 2) - 7 = ${calculationResult}. Can you break down this calculation step by step and verify if it's correct?` 
        }
      ],
      max_tokens: 200
    });
    
    if (aiExplanation.data.data.success) {
      console.log('✅ AI explanation:');
      console.log(aiExplanation.data.data.message?.content);
    }
    
    console.log('\n--- OpenAI-style Function Calling with External APIs ---\n');
    
    // Simulate OpenAI function calling that can use both local and external tools
    const queries = [
      'Calculate the square root of 256',
      'What are the benefits of renewable energy?',
      'What is the factorial of 7?'
    ];
    
    for (const query of queries) {
      console.log(`\nQuery: "${query}"`);
      
      // First try to handle with local tools
      try {
        const localResponse = await client.simulateOpenAIFunctionCalling([
          { role: 'user', content: query }
        ]);
        
        if (localResponse.function_call) {
          console.log(`✅ Handled locally with: ${localResponse.function_call.server}.${localResponse.function_call.name}`);
          console.log(`Result: ${localResponse.content.substring(0, 100)}...`);
        } else {
          // If no local tool can handle it, use external AI
          console.log('🌐 No local tool found, using external AI...');
          
          const externalResponse = await client.callTool('external-api', 'openai_chat_completion', {
            api_base: ELECTRONHUB_API_BASE,
            api_key: ELECTRONHUB_API_KEY,
            model: 'gpt-3.5-turbo',
            messages: [
              { role: 'user', content: query }
            ],
            max_tokens: 150
          });
          
          if (externalResponse.data.data.success) {
            console.log('✅ External AI response:', externalResponse.data.data.message?.content);
          }
        }
      } catch (error) {
        console.log('❌ Error:', error.message);
      }
    }
    
  } catch (error) {
    console.error('❌ Example failed:', error.message);
    if (error.details) {
      console.error('Details:', error.details);
    }
  }
  
  console.log('\n✅ ElectronHub integration example completed');
}

async function startExternalApiServer() {
  console.log('🚀 Starting External API server...');
  
  // Import and start the external API server
  const { spawn } = await import('child_process');
  
  return new Promise((resolve, reject) => {
    const serverProcess = spawn('node', ['examples/external-api-server.js'], {
      stdio: 'inherit'
    });
    
    // Give the server time to start
    setTimeout(() => {
      console.log('✅ External API server should be running on port 3003');
      resolve(serverProcess);
    }, 2000);
    
    serverProcess.on('error', reject);
  });
}

if (import.meta.url === `file://${process.argv[1]}`) {
  console.log('Note: Make sure the external-api server is running on port 3003');
  console.log('You can start it with: node examples/external-api-server.js');
  console.log('Or start all servers with: npm run start:all\n');
  
  electronHubExample().catch(console.error);
}
