import MCPFunctionServer from '../src/server.js';

const server = new MCPFunctionServer();

server.registerTool({
  name: 'text_analyzer',
  description: 'Analyze text and return statistics',
  parameters: {
    properties: {
      text: {
        type: 'string',
        description: 'Text to analyze'
      },
      analysis_type: {
        type: 'string',
        enum: ['basic', 'detailed'],
        description: 'Type of analysis to perform'
      }
    },
    required: ['text']
  },
  handler: async (args) => {
    const { text, analysis_type = 'basic' } = args;
    
    const words = text.split(/\s+/).filter(word => word.length > 0);
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const characters = text.length;
    const charactersNoSpaces = text.replace(/\s/g, '').length;
    
    const basicStats = {
      word_count: words.length,
      sentence_count: sentences.length,
      character_count: characters,
      character_count_no_spaces: charactersNoSpaces
    };
    
    if (analysis_type === 'detailed') {
      const avgWordsPerSentence = words.length / sentences.length || 0;
      const avgCharsPerWord = charactersNoSpaces / words.length || 0;
      const wordFrequency = {};
      
      words.forEach(word => {
        const cleanWord = word.toLowerCase().replace(/[^\w]/g, '');
        wordFrequency[cleanWord] = (wordFrequency[cleanWord] || 0) + 1;
      });
      
      const mostCommonWords = Object.entries(wordFrequency)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([word, count]) => ({ word, count }));
      
      return {
        success: true,
        analysis_type: 'detailed',
        stats: {
          ...basicStats,
          avg_words_per_sentence: Math.round(avgWordsPerSentence * 100) / 100,
          avg_chars_per_word: Math.round(avgCharsPerWord * 100) / 100,
          most_common_words: mostCommonWords
        },
        timestamp: new Date().toISOString()
      };
    }
    
    return {
      success: true,
      analysis_type: 'basic',
      stats: basicStats,
      timestamp: new Date().toISOString()
    };
  }
});

server.registerTool({
  name: 'password_generator',
  description: 'Generate secure passwords',
  parameters: {
    properties: {
      length: {
        type: 'number',
        description: 'Password length (8-128)',
        minimum: 8,
        maximum: 128
      },
      include_symbols: {
        type: 'boolean',
        description: 'Include special symbols',
        default: true
      },
      include_numbers: {
        type: 'boolean',
        description: 'Include numbers',
        default: true
      }
    }
  },
  handler: async (args) => {
    const { 
      length = 12, 
      include_symbols = true, 
      include_numbers = true 
    } = args;
    
    if (length < 8 || length > 128) {
      throw new Error('Password length must be between 8 and 128 characters');
    }
    
    let charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    
    if (include_numbers) {
      charset += '0123456789';
    }
    
    if (include_symbols) {
      charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';
    }
    
    let password = '';
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    
    const strength = length >= 16 ? 'strong' : length >= 12 ? 'medium' : 'weak';
    
    return {
      success: true,
      password,
      length,
      strength,
      includes_symbols: include_symbols,
      includes_numbers: include_numbers,
      timestamp: new Date().toISOString()
    };
  }
});

server.registerTool({
  name: 'base64_encode_decode',
  description: 'Encode or decode base64 strings',
  parameters: {
    properties: {
      text: {
        type: 'string',
        description: 'Text to encode/decode'
      },
      operation: {
        type: 'string',
        enum: ['encode', 'decode'],
        description: 'Operation to perform'
      }
    },
    required: ['text', 'operation']
  },
  handler: async (args) => {
    const { text, operation } = args;
    
    try {
      let result;
      if (operation === 'encode') {
        result = Buffer.from(text, 'utf8').toString('base64');
      } else if (operation === 'decode') {
        result = Buffer.from(text, 'base64').toString('utf8');
      } else {
        throw new Error('Invalid operation. Use "encode" or "decode"');
      }
      
      return {
        success: true,
        operation,
        input: text,
        output: result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw new Error(`${operation} failed: ${error.message}`);
    }
  }
});

console.log('Custom tools registered. Starting server...');
server.start().catch(console.error);
