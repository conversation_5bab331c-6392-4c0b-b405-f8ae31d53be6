import MCPClient from '../client/index.js';

async function runMultiServerTests() {
  console.log('🚀 Starting Multi-Server MCP Function Calling Tests\n');
  
  const client = new MCPClient();
  
  try {
    console.log('1. Connecting to multiple MCP servers...\n');
    
    await client.addServer('default', 'http://localhost:3000');
    await client.addServer('weather', 'http://localhost:3001');
    await client.addServer('math', 'http://localhost:3002');
    
    console.log('\n✅ Connected to all servers\n');

    console.log('2. Listing all available tools...\n');
    const allTools = await client.getAllTools();
    
    for (const [serverName, tools] of Object.entries(allTools)) {
      console.log(`📋 ${serverName} server tools:`);
      tools.forEach(tool => {
        console.log(`  - ${tool.name}: ${tool.description}`);
      });
      console.log();
    }

    console.log('3. Testing direct tool calls on specific servers...\n');
    
    console.log('--- Testing Weather Server ---');
    await client.callTool('weather', 'get_weather', {
      location: 'San Francisco, CA',
      unit: 'celsius'
    });
    
    await client.callTool('weather', 'get_forecast', {
      location: 'New York, NY',
      days: 5,
      unit: 'fahrenheit'
    });
    
    console.log('\n--- Testing Math Server ---');
    await client.callTool('math', 'calculate', {
      expression: '2 + 2 * 3'
    });
    
    await client.callTool('math', 'advanced_math', {
      operation: 'factorial',
      numbers: [5]
    });
    
    await client.callTool('math', 'statistics', {
      data: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
      measures: ['mean', 'median', 'std_dev', 'min', 'max']
    });
    
    console.log('\n--- Testing Default Server ---');
    await client.callTool('default', 'generate_uuid', {
      version: 4
    });
    
    await client.callTool('default', 'echo', {
      message: 'Hello from MCP!'
    });

    console.log('\n4. Testing smart tool finding (auto-discovery)...\n');
    
    console.log('--- Auto-finding tools by name ---');
    await client.findAndCallTool('get_weather', {
      location: 'Tokyo, Japan',
      unit: 'celsius'
    });
    
    await client.findAndCallTool('calculate', {
      expression: 'sqrt(144) + 5'
    });

    console.log('\n5. Testing OpenAI-style function calling simulation...\n');
    
    const testQueries = [
      {
        role: 'user',
        content: 'What is the weather in London?'
      },
      {
        role: 'user', 
        content: 'Calculate 15 * 7 + 3'
      },
      {
        role: 'user',
        content: 'Generate a UUID for me'
      },
      {
        role: 'user',
        content: 'Echo back: Hello World!'
      },
      {
        role: 'user',
        content: 'What is the factorial of 6?'
      }
    ];

    for (const query of testQueries) {
      console.log(`\n--- Processing: "${query.content}" ---`);
      const response = await client.simulateOpenAIFunctionCalling([query]);
      console.log('🤖 AI Response:', response.content);
      if (response.function_call) {
        console.log(`🔧 Called: ${response.function_call.server}.${response.function_call.name}`);
      }
    }

    console.log('\n6. Testing server management...\n');
    
    console.log('📊 Current servers:');
    const servers = client.getServers();
    servers.forEach(server => {
      console.log(`  - ${server.name}: ${server.baseUrl} (${server.info.name})`);
    });
    
    console.log('\n🗑️  Removing weather server...');
    client.removeServer('weather');
    
    console.log('📊 Remaining servers:');
    const remainingServers = client.getServers();
    remainingServers.forEach(server => {
      console.log(`  - ${server.name}: ${server.baseUrl} (${server.info.name})`);
    });

    console.log('\n7. Testing error handling...\n');
    
    try {
      await client.callTool('nonexistent', 'some_tool', {});
    } catch (error) {
      console.log('✅ Correctly caught server not found error:', error.message);
    }
    
    try {
      await client.callTool('math', 'nonexistent_tool', {});
    } catch (error) {
      console.log('✅ Correctly caught tool not found error:', error.message);
    }
    
    try {
      await client.findAndCallTool('nonexistent_tool', {});
    } catch (error) {
      console.log('✅ Correctly caught tool not found anywhere error:', error.message);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.details) {
      console.error('Details:', error.details);
    }
  } finally {
    console.log('\n✅ Multi-server tests completed');
    process.exit(0);
  }
}

async function waitForServers() {
  console.log('⏳ Waiting for servers to start...');
  
  const servers = [
    { name: 'Default', url: 'http://localhost:3000' },
    { name: 'Weather', url: 'http://localhost:3001' },
    { name: 'Math', url: 'http://localhost:3002' }
  ];
  
  for (const server of servers) {
    let attempts = 0;
    const maxAttempts = 10;
    
    while (attempts < maxAttempts) {
      try {
        const response = await fetch(`${server.url}/health`);
        if (response.ok) {
          console.log(`✅ ${server.name} server is ready`);
          break;
        }
      } catch (error) {
        attempts++;
        if (attempts === maxAttempts) {
          console.error(`❌ ${server.name} server failed to start after ${maxAttempts} attempts`);
          console.error(`Please make sure the server is running on ${server.url}`);
          process.exit(1);
        }
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
  }
  
  console.log('🎉 All servers are ready!\n');
}

if (import.meta.url === `file://${process.argv[1]}`) {
  await waitForServers();
  runMultiServerTests().catch(console.error);
}
