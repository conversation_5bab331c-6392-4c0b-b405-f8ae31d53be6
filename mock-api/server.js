import express from 'express';
import cors from 'cors';
import axios from 'axios';

const app = express();
const PORT = 4000;

// Middleware
app.use(cors());
app.use(express.json());

// AI Configuration
const ELECTRONHUB_API_BASE = 'https://api.electronhub.ai/v1';
const ELECTRONHUB_API_KEY = 'ek-proxy-R4tBYVMxBMz7cH3dz3yfg5utVNaYuugooC4vQi2hB3hd';

// AI-powered vault data generation
const vaultCategories = ['Assembly', 'Document', 'Part', 'Drawing', 'Presentation', 'Spreadsheet'];
const vaultStates = ['Work in Progress', 'Released', 'Obsolete', 'For Review', 'Approved'];
const stateColors = {
  'Work in Progress': -12859030,
  'Released': -16744448,
  'Obsolete': -6710887,
  'For Review': -256,
  'Approved': -16711936
};
const categoryColors = {
  'Assembly': -10188850,
  'Document': -3251596,
  'Part': -65536,
  'Drawing': -16776961,
  'Presentation': -8355712,
  'Spreadsheet': -32768
};

// Generate AI-powered vault item data
async function generateVaultItems(vaultId, searchQuery, options = {}) {
  const prompt = `Generate realistic Autodesk Vault item versions for vault ID ${vaultId}.

Search query: "${searchQuery || 'all items'}"
Options: ${JSON.stringify(options)}

Generate 3-8 realistic vault items that would match this search. Include:
- Engineering/CAD items like assemblies, parts, drawings
- Various categories: ${vaultCategories.join(', ')}
- Different states: ${vaultStates.join(', ')}
- Realistic names, numbers, revisions
- Recent modification dates
- Proper version numbers

Return a JSON array of items with this structure:
[
  {
    "name": "realistic_item_name",
    "number": "item_number",
    "title": "item_title",
    "description": "brief_description",
    "category": "category_from_list",
    "state": "state_from_list",
    "revision": "A|B|C|1|2",
    "version": 1,
    "lastModifiedUserName": "realistic_username"
  }
]

Make items relevant to the search query if provided.`;

  try {
    const response = await axios.post(`${ELECTRONHUB_API_BASE}/chat/completions`, {
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: prompt }],
      max_tokens: 1500,
      temperature: 0.7
    }, {
      headers: {
        'Authorization': `Bearer ${ELECTRONHUB_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    const aiResponse = response.data.choices[0]?.message?.content;

    // Parse AI response
    const jsonMatch = aiResponse.match(/\[[\s\S]*\]/);
    if (jsonMatch) {
      const items = JSON.parse(jsonMatch[0]);
      return items.map((item, index) => createVaultItemVersion(item, vaultId, index + 50));
    }
  } catch (error) {
    console.error('AI generation failed:', error.message);
  }

  // Fallback to static data
  return createFallbackVaultItems(vaultId, searchQuery);
}

// Create a properly formatted vault item version
function createVaultItemVersion(aiItem, vaultId, baseId) {
  const id = (baseId + Math.floor(Math.random() * 100)).toString();
  const itemId = (baseId - 5 + Math.floor(Math.random() * 10)).toString();

  return {
    name: aiItem.name || `Item_${id}`,
    id: id,
    revision: aiItem.revision || 'A',
    lastModifiedUserName: aiItem.lastModifiedUserName || 'VaultUser',
    lastModifiedDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
    number: aiItem.number || aiItem.name || `${id}`,
    title: aiItem.title || aiItem.name || '',
    description: aiItem.description || '',
    comment: '',
    state: aiItem.state || vaultStates[Math.floor(Math.random() * vaultStates.length)],
    stateColor: stateColors[aiItem.state] || stateColors['Work in Progress'],
    category: aiItem.category || vaultCategories[Math.floor(Math.random() * vaultCategories.length)],
    categoryColor: categoryColors[aiItem.category] || categoryColors['Document'],
    isReadOnly: false,
    isCloaked: false,
    item: {
      id: itemId,
      entityType: 'Item',
      url: `/AutodeskDM/Services/api/vault/v2/vaults/${vaultId}/items/${itemId}`
    },
    version: aiItem.version || Math.floor(Math.random() * 5) + 1,
    entityType: 'ItemVersion',
    url: `/AutodeskDM/Services/api/vault/v2/vaults/${vaultId}/item-versions/${id}`
  };
}

// Fallback vault items when AI fails
function createFallbackVaultItems(vaultId, searchQuery) {
  const fallbackItems = [
    {
      name: 'Assembly1',
      number: 'Assembly1',
      title: 'Main Assembly',
      description: 'Primary assembly component',
      category: 'Assembly',
      state: 'Work in Progress',
      revision: 'A',
      version: 2,
      lastModifiedUserName: 'TestAccount'
    },
    {
      name: '100001',
      number: '100001',
      title: 'Technical Drawing',
      description: 'Engineering drawing document',
      category: 'Document',
      state: 'Released',
      revision: 'B',
      version: 1,
      lastModifiedUserName: 'Engineer1'
    },
    {
      name: 'Motor_Housing',
      number: 'MH-2024-001',
      title: 'Motor Housing Part',
      description: 'Aluminum motor housing component',
      category: 'Part',
      state: 'For Review',
      revision: 'A',
      version: 3,
      lastModifiedUserName: 'Designer2'
    }
  ];

  return fallbackItems
    .filter(item => !searchQuery ||
      item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.category.toLowerCase().includes(searchQuery.toLowerCase()))
    .map((item, index) => createVaultItemVersion(item, vaultId, 50 + index * 10));
}

// Authentication middleware for Vault API
const authenticateVaultToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];

  // Vault API requires Bearer token from OAuth three-legged flow
  if (authHeader?.startsWith('Bearer ')) {
    const token = authHeader.substring(7);
    if (token && token.length > 10) { // Basic token validation
      next();
    } else {
      res.status(401).json({
        error: "unauthorized",
        message: "The supplied authorization header was not valid or the supplied token scope was not acceptable.",
        timestamp: new Date().toISOString()
      });
    }
  } else {
    res.status(401).json({
      error: "unauthorized",
      message: "Must be Bearer <token>, where <token> is obtained via a three-legged OAuth flow.",
      timestamp: new Date().toISOString()
    });
  }
};

// Helper functions for vault operations
const applyVaultFilters = (items, filters) => {
  let filtered = [...items];

  // Apply search query
  if (filters.q) {
    const query = filters.q.toLowerCase();
    filtered = filtered.filter(item =>
      item.name.toLowerCase().includes(query) ||
      item.title.toLowerCase().includes(query) ||
      item.description.toLowerCase().includes(query) ||
      item.number.toLowerCase().includes(query) ||
      item.category.toLowerCase().includes(query)
    );
  }

  // Apply released items only filter
  if (filters['option[releasedItemsOnly]'] === 'true') {
    filtered = filtered.filter(item => item.state === 'Released');
  }

  // Apply latest only filter (default true)
  if (filters['option[latestOnly]'] !== 'false') {
    // Group by item ID and keep only latest version
    const latestVersions = {};
    filtered.forEach(item => {
      const itemId = item.item.id;
      if (!latestVersions[itemId] || item.version > latestVersions[itemId].version) {
        latestVersions[itemId] = item;
      }
    });
    filtered = Object.values(latestVersions);
  }

  return filtered;
};

const applySorting = (items, sortParam) => {
  if (!sortParam) return items;

  const sortCriteria = sortParam.split(',').map(s => s.trim());

  return items.sort((a, b) => {
    for (const criterion of sortCriteria) {
      const [field, order = 'asc'] = criterion.split(' ');
      let aVal = a[field] || '';
      let bVal = b[field] || '';

      // Handle different field types
      if (field === 'lastModifiedDate') {
        aVal = new Date(aVal);
        bVal = new Date(bVal);
      } else if (field === 'version') {
        aVal = parseInt(aVal);
        bVal = parseInt(bVal);
      }

      let comparison = 0;
      if (aVal > bVal) comparison = 1;
      if (aVal < bVal) comparison = -1;

      if (order.toLowerCase() === 'desc') comparison *= -1;

      if (comparison !== 0) return comparison;
    }
    return 0;
  });
};

const createPagination = (items, limit, cursorState) => {
  const maxLimit = Math.min(parseInt(limit) || 1000, 1000);
  const paginatedItems = items.slice(0, maxLimit);

  return {
    pagination: {
      limit: maxLimit,
      totalResults: items.length,
      indexingStatus: 'IndexingComplete',
      ...(cursorState && { cursorState })
    },
    results: paginatedItems,
    included: {}
  };
};

// Routes

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    message: 'Autodesk Vault API (AI-Powered Mock) is running',
    timestamp: new Date().toISOString(),
    version: '2.0.0',
    api: 'AutodeskDM/Services/api/vault/v2',
    features: ['AI-powered item generation', 'OAuth Bearer authentication', 'Advanced search and filtering']
  });
});

// Vault API endpoint - Get item versions
app.get('/AutodeskDM/Services/api/vault/v2/vaults/:vaultId/item-versions', authenticateVaultToken, async (req, res) => {
  try {
    const { vaultId } = req.params;
    const {
      q,
      'option[propDefIds]': propDefIds,
      'option[releasedItemsOnly]': releasedItemsOnly,
      'option[latestOnly]': latestOnly,
      sort,
      limit,
      cursorState
    } = req.query;

    console.log(`🔍 Vault ${vaultId} item-versions request:`, {
      search: q,
      releasedOnly: releasedItemsOnly,
      latestOnly: latestOnly,
      sort,
      limit
    });

    // Generate AI-powered vault items
    const items = await generateVaultItems(vaultId, q, {
      releasedItemsOnly,
      latestOnly,
      propDefIds
    });

    // Apply filters
    const filteredItems = applyVaultFilters(items, {
      q,
      'option[releasedItemsOnly]': releasedItemsOnly,
      'option[latestOnly]': latestOnly
    });

    // Apply sorting
    const sortedItems = applySorting(filteredItems, sort);

    // Create paginated response
    const response = createPagination(sortedItems, limit, cursorState);

    console.log(`✅ Returning ${response.results.length} vault items`);
    res.json(response);

  } catch (error) {
    console.error('❌ Vault API error:', error.message);
    res.status(500).json({
      error: "internal_server_error",
      message: "An error occurred while processing the vault request",
      timestamp: new Date().toISOString()
    });
  }
});



// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: "internal_server_error",
    message: "Something went wrong!",
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: "not_found",
    message: "Endpoint not found",
    timestamp: new Date().toISOString()
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Autodesk Vault API (AI-Powered Mock) running on port ${PORT}`);
  console.log(`📋 Health check: http://localhost:${PORT}/health`);
  console.log(`🔗 Base URL: http://localhost:${PORT}/AutodeskDM/Services/api/vault/v2`);
  console.log(`🏗️  Available endpoints:`);
  console.log(`   - GET /vaults/{vaultId}/item-versions - Get vault item versions`);
  console.log(`🤖 AI Features:`);
  console.log(`   - Dynamic vault item generation based on search queries`);
  console.log(`   - Realistic engineering/CAD item data`);
  console.log(`   - Context-aware responses`);
  console.log(`🔐 Authentication: Bearer token required (OAuth three-legged flow)`);
  console.log(`📖 Example: curl -H "Authorization: Bearer your-token" http://localhost:${PORT}/AutodeskDM/Services/api/vault/v2/vaults/117/item-versions`);
});
