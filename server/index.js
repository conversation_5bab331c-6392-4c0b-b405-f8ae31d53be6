import express from 'express';
import cors from 'cors';
import { v4 as uuidv4 } from 'uuid';
import { 
  MCPTool, 
  MCPError, 
  MCPMessageTypes, 
  createSuccessResponse, 
  createErrorResponse,
  validateToolParameters 
} from '../shared/types.js';

class MCPServer {
  constructor(config = {}) {
    this.config = {
      name: config.name || 'MCP Server',
      version: config.version || '1.0.0',
      description: config.description || 'HTTP-based MCP Server',
      port: config.port || 3000,
      ...config
    };
    
    this.app = express();
    this.tools = new Map();
    this.setupMiddleware();
    this.setupRoutes();
    this.registerDefaultTools();
  }

  setupMiddleware() {
    this.app.use(cors());
    this.app.use(express.json());
    
    this.app.use((req, res, next) => {
      console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
      next();
    });
  }

  setupRoutes() {
    this.app.get('/health', (req, res) => {
      res.json(createSuccessResponse({
        status: 'healthy',
        server: this.config.name,
        version: this.config.version,
        uptime: process.uptime()
      }));
    });

    this.app.get('/server/info', (req, res) => {
      res.json(createSuccessResponse({
        name: this.config.name,
        version: this.config.version,
        description: this.config.description,
        capabilities: ['tools'],
        toolCount: this.tools.size
      }));
    });

    this.app.get('/tools/list', (req, res) => {
      try {
        const tools = Array.from(this.tools.values()).map(tool => tool.getSchema());
        res.json(createSuccessResponse({ tools }));
      } catch (error) {
        res.status(500).json(createErrorResponse(error));
      }
    });

    this.app.post('/tools/call', async (req, res) => {
      try {
        const { name, arguments: args = {} } = req.body;
        
        if (!name) {
          throw new MCPError('Tool name is required', 'MISSING_TOOL_NAME');
        }

        const tool = this.tools.get(name);
        if (!tool) {
          throw new MCPError(
            `Tool '${name}' not found`, 
            'TOOL_NOT_FOUND',
            { availableTools: Array.from(this.tools.keys()) }
          );
        }

        validateToolParameters(tool, args);
        const result = await tool.execute(args);
        
        res.json(createSuccessResponse(result, { tool: name }));
      } catch (error) {
        const statusCode = error instanceof MCPError ? 400 : 500;
        res.status(statusCode).json(createErrorResponse(error));
      }
    });

    this.app.use('*', (req, res) => {
      res.status(404).json(createErrorResponse(
        new MCPError('Endpoint not found', 'NOT_FOUND', { path: req.originalUrl })
      ));
    });
  }

  registerTool(name, description, parameters, handler) {
    if (typeof name === 'object') {
      const toolDef = name;
      const tool = new MCPTool(
        toolDef.name,
        toolDef.description,
        toolDef.parameters,
        toolDef.handler
      );
      this.tools.set(toolDef.name, tool);
      console.log(`Registered tool: ${toolDef.name}`);
    } else {
      const tool = new MCPTool(name, description, parameters, handler);
      this.tools.set(name, tool);
      console.log(`Registered tool: ${name}`);
    }
  }

  registerDefaultTools() {
    this.registerTool({
      name: 'get_server_info',
      description: 'Get information about this MCP server',
      parameters: {
        properties: {},
        required: []
      },
      handler: async () => {
        return {
          name: this.config.name,
          version: this.config.version,
          description: this.config.description,
          uptime: process.uptime(),
          toolCount: this.tools.size,
          tools: Array.from(this.tools.keys())
        };
      }
    });

    this.registerTool({
      name: 'generate_uuid',
      description: 'Generate a new UUID',
      parameters: {
        properties: {
          version: {
            type: 'number',
            enum: [1, 4],
            description: 'UUID version (1 or 4)',
            default: 4
          }
        },
        required: []
      },
      handler: async (args) => {
        const { version = 4 } = args;
        return {
          uuid: uuidv4(),
          version,
          timestamp: new Date().toISOString()
        };
      }
    });

    this.registerTool({
      name: 'echo',
      description: 'Echo back the provided message',
      parameters: {
        properties: {
          message: {
            type: 'string',
            description: 'Message to echo back'
          }
        },
        required: ['message']
      },
      handler: async (args) => {
        const { message } = args;
        return {
          original: message,
          echoed: message,
          length: message.length,
          timestamp: new Date().toISOString()
        };
      }
    });
  }

  start() {
    return new Promise((resolve) => {
      this.server = this.app.listen(this.config.port, () => {
        console.log(`🚀 ${this.config.name} started on port ${this.config.port}`);
        console.log(`📋 Available tools: ${Array.from(this.tools.keys()).join(', ')}`);
        console.log(`🔗 Health check: http://localhost:${this.config.port}/health`);
        console.log(`📖 Server info: http://localhost:${this.config.port}/server/info`);
        console.log(`🛠️  Tools list: http://localhost:${this.config.port}/tools/list`);
        resolve();
      });
    });
  }

  stop() {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          console.log(`${this.config.name} stopped`);
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  const server = new MCPServer({
    name: 'Default MCP Server',
    description: 'A basic MCP server with default tools',
    port: 3000
  });
  
  server.start().catch(console.error);
  
  process.on('SIGINT', async () => {
    console.log('\nShutting down server...');
    await server.stop();
    process.exit(0);
  });
}

export default MCPServer;
