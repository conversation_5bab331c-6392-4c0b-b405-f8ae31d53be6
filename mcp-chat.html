<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP Chat Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 80vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: #4f46e5;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .chat-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8fafc;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }

        .message.user .message-content {
            background: #4f46e5;
            color: white;
        }

        .message.assistant .message-content {
            background: white;
            border: 1px solid #e2e8f0;
            color: #1a202c;
        }

        .message.system .message-content {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            color: #92400e;
            font-size: 12px;
        }

        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e2e8f0;
        }

        .input-container {
            display: flex;
            gap: 10px;
        }

        #messageInput {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 25px;
            outline: none;
            font-size: 16px;
            transition: border-color 0.2s;
        }

        #messageInput:focus {
            border-color: #4f46e5;
        }

        #sendButton {
            padding: 12px 24px;
            background: #4f46e5;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: background 0.2s;
        }

        #sendButton:hover:not(:disabled) {
            background: #4338ca;
        }

        #sendButton:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 10px;
            color: #6b7280;
        }

        .tool-info {
            font-size: 11px;
            color: #6b7280;
            margin-top: 5px;
            font-style: italic;
        }

        .error {
            color: #dc2626;
            background: #fef2f2;
            border: 1px solid #fecaca;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>🤖 MCP Chat Interface</h1>
            <p>Chat with AI using MCP tools and functions</p>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message system">
                <div class="message-content">
                    Welcome! I can help you with various tasks using MCP tools. Try asking me to:
                    <br>• Find available pets in the store
                    <br>• Add a new pet
                    <br>• Ask general questions
                    <br>• And more!
                </div>
            </div>
        </div>
        
        <div class="loading" id="loading">
            <div>🤔 Thinking...</div>
        </div>
        
        <div class="chat-input">
            <div class="input-container">
                <input 
                    type="text" 
                    id="messageInput" 
                    placeholder="Type your message here..."
                    autocomplete="off"
                >
                <button id="sendButton">Send</button>
            </div>
        </div>
    </div>

    <script>
        const chatMessages = document.getElementById('chatMessages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const loading = document.getElementById('loading');

        const MCP_CLIENT_URL = 'http://localhost:8000';

        function addMessage(content, role = 'user', toolInfo = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;
            
            if (toolInfo) {
                const toolDiv = document.createElement('div');
                toolDiv.className = 'tool-info';
                toolDiv.textContent = `🔧 Used: ${toolInfo}`;
                contentDiv.appendChild(toolDiv);
            }
            
            messageDiv.appendChild(contentDiv);
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function addErrorMessage(error) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message assistant';
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content error';
            contentDiv.textContent = `Error: ${error}`;
            
            messageDiv.appendChild(contentDiv);
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function setLoading(isLoading) {
            loading.style.display = isLoading ? 'block' : 'none';
            sendButton.disabled = isLoading;
            messageInput.disabled = isLoading;
        }

        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            // Add user message
            addMessage(message, 'user');
            messageInput.value = '';
            setLoading(true);

            try {
                const response = await fetch(`${MCP_CLIENT_URL}/v1/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        model: 'mcp-1',
                        messages: [
                            { role: 'user', content: message }
                        ]
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                const assistantMessage = data.choices[0]?.message?.content || 'No response';
                const toolUsed = data.mcp_tool_used;
                
                let toolInfo = null;
                if (toolUsed) {
                    toolInfo = toolUsed;
                    if (data.mcp_parameters) {
                        const params = Object.entries(data.mcp_parameters)
                            .map(([k, v]) => `${k}: ${v}`)
                            .join(', ');
                        if (params) {
                            toolInfo += ` (${params})`;
                        }
                    }
                }

                addMessage(assistantMessage, 'assistant', toolInfo);

            } catch (error) {
                console.error('Error:', error);
                addErrorMessage(error.message);
            } finally {
                setLoading(false);
                messageInput.focus();
            }
        }

        // Event listeners
        sendButton.addEventListener('click', sendMessage);
        
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Focus input on load
        messageInput.focus();

        // Check if MCP client is running
        fetch(`${MCP_CLIENT_URL}/health`)
            .then(response => response.json())
            .then(data => {
                addMessage('✅ Connected to MCP client successfully!', 'system');
            })
            .catch(error => {
                addErrorMessage('Failed to connect to MCP client. Make sure it\'s running on port 8000.');
            });
    </script>
</body>
</html>
